 import { createRouter, create<PERSON>eb<PERSON><PERSON>ory, RouteRecordRaw } from 'vue-router'

// 定义路由
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/public',
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
    },
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/register/index.vue'),
    meta: {
      title: '注册',
      requiresAuth: false,
    },
  },
  {
    path: '/forget-password',
    name: 'ForgetPassword',
    component: () => import('@/views/forget-password/index.vue'),
    meta: {
      title: '忘记密码',
      requiresAuth: false,
    },
  },
  // 后台管理路由
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/layout/index.vue'),
    redirect: '/dashboard/home',
    meta: {
      title: '控制台',
      requiresAuth: true,
    },
    children: [
      {
        path: 'home',
        name: 'AdminHome',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '首页',
          requiresAuth: true,
        },
      },
      {
        path: 'user',
        name: 'User',
        component: () => import('@/views/user/index.vue'),
        meta: {
          title: '用户管理',
          requiresAuth: true,
        },
      },
      {
        path: 'warehouse',
        name: 'Warehouse',
        component: () => import('@/views/warehouse/index.vue'),
        meta: {
          title: '仓库管理',
          requiresAuth: true,
        },
      },
      {
        path: 'article',
        name: 'Article',
        component: () => import('@/views/article/index.vue'),
        meta: {
          title: '文章管理',
          requiresAuth: true,
        },
      },
      {
        path: 'advertisement',
        name: 'Advertisement',
        component: () => import('@/views/advertisement/index.vue'),
        meta: {
          title: '广告管理',
          requiresAuth: true,
        },
      },
      {
        path: 'store',
        name: 'Store',
        component: () => import('@/views/store/index.vue'),
        meta: {
          title: '商城管理',
          requiresAuth: true,
        },
      },
      {
        path: 'custom-orders',
        name: 'CustomOrders',
        component: () => import('@/views/custom-orders/index.vue'),
        meta: {
          title: '定制订单管理',
          requiresAuth: true,
        },
      },
      {
        path: 'homepage-data',
        name: 'HomepageData',
        component: () => import('@/views/homepage-data/index.vue'),
        meta: {
          title: '首页数据管理',
          requiresAuth: true,
        },
      },
      {
        path: 'system-settings',
        name: 'SystemSettings',
        component: () => import('@/views/system-settings/index.vue'),
        meta: {
          title: '系统设置',
          requiresAuth: true,
        },
      },
    ],
  },
  // 前台公共页面路由
  {
    path: '/public',
    name: 'Public',
    component: () => import('@/views/public/layout/index.vue'),
    redirect: '/public/home',
    meta: {
      title: '棉花棠',
      requiresAuth: false,
    },
    children: [
      {
        path: 'home',
        name: 'PublicHome',
        component: () => import('@/views/public/home/<USER>'),
        meta: {
          title: '首页',
          requiresAuth: false,
        },
      },
      {
        path: 'products',
        name: 'Products',
        component: () => import('@/views/public/products/index.vue'),
        meta: {
          title: '产品展示',
          requiresAuth: false,
        },
      },
      {
        path: 'products/:id',
        name: 'ProductDetail',
        component: () => import('@/views/public/products/detail.vue'),
        meta: {
          title: '产品详情',
          requiresAuth: false,
        },
      },
      {
        path: 'about',
        name: 'About',
        component: () => import('@/views/public/about/index.vue'),
        meta: {
          title: '关于我们',
          requiresAuth: false,
        },
      },
      {
        path: 'contact',
        name: 'Contact',
        component: () => import('@/views/public/contact/index.vue'),
        meta: {
          title: '联系我们',
          requiresAuth: false,
        },
      },
      {
        path: 'news',
        name: 'News',
        component: () => import('@/views/public/news/index.vue'),
        meta: {
          title: '新闻动态',
          requiresAuth: false,
        },
      },
      {
        path: 'news/:id',
        name: 'NewsDetail',
        component: () => import('@/views/public/news/detail.vue'),
        meta: {
          title: '新闻详情',
          requiresAuth: false,
        },
      },
      {
        path: 'custom-orders',
        name: 'PublicCustomOrders',
        component: () => import('@/views/public/custom-orders/index.vue'),
        meta: {
          title: '个性需求',
          requiresAuth: true,
        },
      },
      {
        path: 'favorites',
        name: 'PublicFavorites',
        component: () => import('@/views/public/favorites/index.vue'),
        meta: {
          title: '我的收藏',
          requiresAuth: true,
        },
      },
    ],
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '404',
      requiresAuth: false,
    },
  },
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
})

// 导航守卫
router.beforeEach((to, from, next) => {
  // 设置文档标题
  document.title = to.meta.title as string || '棉花棠交易平台'

  // 检查路由是否需要认证
  if (to.meta.requiresAuth) {
    const token = localStorage.getItem('token')
    if (token) {
      next()
    } else {
      // 未登录，重定向到登录页
      next('/login')
    }
  } else {
    // 如果已登录用户访问登录页，重定向到首页
    if (to.path === '/login' || to.path === '/register') {
      const token = localStorage.getItem('token')
      if (token) {
        next('/public/home')
      } else {
        next()
      }
    } else {
      next()
    }
  }
})

export default router
