<template>
  <div class="bg_f min_w">
    <div class="m_auto padt_15 clearfix">
      <!-- Logo -->
      <div class="fl">
        <router-link to="/public/home">
          <img src="/images/logo.png" class="dis_b" style="height:78px;" />
        </router-link>
      </div>
      
      <!-- 搜索区域 -->
      <div class="fl ml_50 pos_rela">
        <!-- 批量搜索框 -->
        <div style="position: absolute;top:55px;left:0;" class="dis_n" id="pl_box" ref="plBox">
          <textarea
            style="width:400px;height: 150px;"
            id="pl_text"
            v-model="batchText"
            placeholder="每行输入一个批次/捆号"
            class="border1 dis_b pad_10"
          ></textarea>
          <input
            type="button"
            id="pl_sou"
            value="搜索"
            class="dis_b bg_ora"
            style="color: #fff;padding:5px 10px;border:0"
            @click="handleBatchSearch"
          />
        </div>
        
        <!-- 搜索表单 -->
        <form action="#" id="ss_fm" style="margin-top:9px;" @submit.prevent="handleSearch">
          <input
            type="search"
            name="sk"
            id="sousuo_sk"
            v-model="searchKeyword"
            class="clr_9"
            autocomplete="off"
            placeholder="批号/轧花厂/仓库/产棉地区"
            style="height: 40px;border:2px solid #28afe5;width:500px;padding:10px;box-sizing: border-box;"
          >
          <button type="submit" class="send" style="height: 40px;outline: none;cursor: pointer;">
            <img src="/images/searth.png" style="width:20px;">
          </button>
          <span
            class="f_14 clr_r pointer"
            id="pl_click"
            style="position: absolute;top:30px;right:80px;"
            @click="toggleBatchBox"
          >批量</span>
        </form>
      </div>
      
      <!-- 用户操作区域 -->
      <div class="fr al_rt f_14 login">
        <div class="mt_10">
          <!-- 未登录状态 -->
          <template v-if="!isLoggedIn">
            <router-link to="/register" class="clr_d f_16">注册</router-link>
            <router-link to="/login" class="clr_d f_16 log_a">登录</router-link>
          </template>
          
          <!-- 已登录状态 -->
          <template v-else>
            <router-link to="/user/profile" class="clr_d f_16">会员中心</router-link>
            <a @click="handleLogout" class="clr_d f_16 log_a pointer">退出</a>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { logout } from '@/api/auth'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const searchKeyword = ref('')
const batchText = ref('')
const plBox = ref<HTMLElement>()

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 搜索功能
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  // 跳转到搜索结果页面
  router.push({
    path: '/public/products',
    query: { keyword: searchKeyword.value.trim() }
  })
}

// 批量搜索功能
const handleBatchSearch = () => {
  if (!batchText.value.trim()) {
    ElMessage.warning('请输入批次/捆号')
    return
  }
  
  const batches = batchText.value.trim().split('\n').filter(item => item.trim())
  if (batches.length === 0) {
    ElMessage.warning('请输入有效的批次/捆号')
    return
  }
  
  // 跳转到批量搜索结果页面
  router.push({
    path: '/public/products',
    query: { batches: batches.join(',') }
  })
  
  // 隐藏批量搜索框
  toggleBatchBox()
}

// 切换批量搜索框显示/隐藏
const toggleBatchBox = () => {
  if (plBox.value) {
    const isVisible = plBox.value.style.display !== 'none'
    plBox.value.style.display = isVisible ? 'none' : 'block'
  }
}

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确认退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用退出登录API
    await logout()
    userStore.logout()
    
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('退出登录失败')
    }
  }
}
</script>

<style scoped>
/* 搜索按钮样式 */
.send {
  background: #28afe5;
  border: 2px solid #28afe5;
  width: 40px;
  margin-left: -2px;
  vertical-align: top;
}

.send:hover {
  background: #1e9bd1;
  border-color: #1e9bd1;
}

/* 登录链接样式 */
.log_a {
  margin-left: 20px;
}

.log_a:hover,
.clr_d:hover {
  color: #28afe5 !important;
}

/* 批量搜索框样式 */
#pl_box {
  background: #fff;
  border: 1px solid #ddd;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  z-index: 1000;
}

#pl_box textarea {
  resize: vertical;
}

#pl_sou {
  margin-top: 10px;
  cursor: pointer;
}

#pl_sou:hover {
  background: #e55a00 !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .min_w {
    min-width: auto;
  }
  
  .m_auto {
    padding: 0 15px;
  }
  
  .ml_50 {
    margin-left: 20px;
  }
  
  #sousuo_sk {
    width: 300px !important;
  }
  
  #pl_box {
    width: 300px;
  }
  
  #pl_box textarea {
    width: 280px !important;
  }
}
</style>
