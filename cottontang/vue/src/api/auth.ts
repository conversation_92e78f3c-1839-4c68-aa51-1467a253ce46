import request from '@/utils/request'

// 用户登录
export function login(data: {
  username: string
  password: string
  captcha?: string
  remember?: boolean
}) {
  return request({
    url: '/auth/login',
    method: 'post',
    data
  })
}

// 用户注册
export function register(data: {
  username: string
  password: string
  mobile_code: string
  real_name: string
  company: string
}) {
  return request({
    url: '/auth/register',
    method: 'post',
    data
  })
}

// 发送短信验证码
export function sendSmsCode(mobile: string) {
  return request({
    url: '/auth/send-sms',
    method: 'post',
    data: { mobile }
  })
}

// 退出登录
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/auth/user-info',
    method: 'get'
  })
}

// 刷新token
export function refreshToken() {
  return request({
    url: '/auth/refresh',
    method: 'post'
  })
}

// 修改密码
export function changePassword(data: {
  oldPassword: string
  newPassword: string
}) {
  return request({
    url: '/auth/change-password',
    method: 'post',
    data
  })
}

// 忘记密码
export function forgetPassword(data: {
  username: string
  mobile_code: string
  password: string
}) {
  return request({
    url: '/auth/runForget',
    method: 'post',
    data
  })
}

// 检查用户名是否存在
export function checkUsername(username: string) {
  return request({
    url: '/auth/check-username',
    method: 'post',
    data: { username }
  })
}
