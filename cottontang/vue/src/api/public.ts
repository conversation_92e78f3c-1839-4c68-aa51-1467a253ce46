import request from '@/utils/request'

// 获取热门产品
export function getHotProducts(limit = 8) {
  return request({
    url: '/public/hot-products',
    method: 'get',
    params: {
      limit
    }
  })
}

// 获取新闻列表
export function getNewsList(limit = 6) {
  return request({
    url: '/public/news',
    method: 'get',
    params: {
      limit
    }
  })
}

// 根据ID获取新闻详情
export function getNewsById(id: string) {
  return request({
    url: `/public/news/${id}`,
    method: 'get'
  })
}

// 获取产品分类
export function getProductCategories() {
  return request({
    url: '/public/categories',
    method: 'get'
  })
}

// 获取公司信息
export function getCompanyInfo() {
  return request({
    url: '/public/company-info',
    method: 'get'
  })
}

// 获取轮播图
export function getBanners() {
  return request({
    url: '/public/banners',
    method: 'get'
  })
}

// 提交联系表单
export function submitContactForm(data: any) {
  return request({
    url: '/public/contact',
    method: 'post',
    data
  })
}
