import request from '@/utils/request'

// 获取产品列表
export const getProducts = (params: any) => {
  return request({
    url: '/stores/index',
    method: 'get',
    params
  })
}

// 获取筛选选项
export const getFilterOptions = () => {
  return request({
    url: '/stores/filter-options',
    method: 'get'
  })
}

// 获取产品详情
export const getProductDetail = (id: number) => {
  return request({
    url: `/stores/view/${id}`,
    method: 'get'
  })
}

// 添加到收藏
export const addToFavorites = (id: number) => {
  return request({
    url: '/stores/add-favor',
    method: 'post',
    data: { id }
  })
}

// 取消收藏
export const removeFromFavorites = (id: number) => {
  return request({
    url: '/stores/cancel-favor',
    method: 'post',
    data: { id }
  })
}

// 导出产品数据
export const exportProducts = (params: any) => {
  return request({
    url: '/stores/export',
    method: 'get',
    params
  })
}

// 获取子产地数据
export const getChildChandi = (id: number) => {
  return request({
    url: '/tools/get-cat',
    method: 'get',
    params: { id }
  })
}

// 获取交货地数据
export const getJiaohuodi = (id: number) => {
  return request({
    url: '/tools/get-jhd',
    method: 'get',
    params: { id }
  })
}

// 添加到个性需求
export const addToDingzhi = (params: any) => {
  return request({
    url: '/stores/r-dingzhi',
    method: 'post',
    data: params
  })
}
