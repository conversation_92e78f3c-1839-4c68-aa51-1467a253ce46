import request from '@/utils/request'

// 获取商品列表
export function getStoreList(params: any) {
  return request({
    url: '/store/list',
    method: 'get',
    params
  })
}

// 根据ID获取商品详情
export function getStoreById(id: string) {
  return request({
    url: `/store/${id}`,
    method: 'get'
  })
}

// 创建商品
export function createStore(data: any) {
  return request({
    url: '/store',
    method: 'post',
    data
  })
}

// 更新商品
export function updateStore(id: string, data: any) {
  return request({
    url: `/store/${id}`,
    method: 'put',
    data
  })
}

// 删除商品
export function deleteStore(id: string) {
  return request({
    url: `/store/${id}`,
    method: 'delete'
  })
}

// 更新商品状态
export function updateStoreStatus(id: string, status: number) {
  return request({
    url: `/store/${id}/status`,
    method: 'put',
    params: {
      status
    }
  })
}

// 获取商品分类列表
export function getCategoryList() {
  return request({
    url: '/category/list',
    method: 'get'
  })
}
