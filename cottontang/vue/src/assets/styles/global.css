/* 全局样式 - 完全按照PHP原版 */

/* 基础样式 */
.bg_f2 { background-color: #f2f2f2; }
.bg_f { background-color: #fff; }
.bg_b { background-color: #000; }

.clr_f { color: #fff; }
.clr_3 { color: #333; }
.clr_9 { color: #999; }
.clr_b { color: #0066cc; }
.clr_r { color: #ff0000; }

.f_16 { font-size: 16px; }
.f_14 { font-size: 14px; }
.f_wei { font-weight: bold; }

/* 布局样式 */
.m_auto {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.mt_10 { margin-top: 10px; }
.mt_20 { margin-top: 20px; }
.mb_10 { margin-bottom: 10px; }
.mb_20 { margin-bottom: 20px; }
.mr_10 { margin-right: 10px; }
.mr_20 { margin-right: 20px; }
.ml_20 { margin-left: 20px; }

.pad_5 { padding: 5px; }
.pad_10 { padding: 10px; }
.pad_20 { padding: 20px; }
.pad_30 { padding: 30px; }

.w_100p { width: 100%; }
.w_80p { width: 80%; }
.w_20p { width: 20%; }

/* 浮动和定位 */
.fl { float: left; }
.fr { float: right; }
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

.pos_rela { position: relative; }
.pos_abs { position: absolute; }

/* 显示样式 */
.dis_ib { display: inline-block; }
.dis_b { display: block; }
.ver_mid { vertical-align: middle; }

/* 文本对齐 */
.al_ct { text-align: center; }
.al_rt { text-align: right; }
.al_lt { text-align: left; }

/* 行高 */
.lh_28 { line-height: 28px; }
.lh_30 { line-height: 30px; }

/* 其他 */
.pointer { cursor: pointer; }
.border1 { border: 1px solid #e4e4e4; }

/* 链接样式 */
a {
  text-decoration: none;
  color: inherit;
}

a:hover {
  text-decoration: none;
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  text-decoration: none;
  transition: all 0.3s;
}

.btn:hover {
  opacity: 0.8;
}

/* 表格样式重置 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .m_auto {
    padding: 0 10px;
  }
  
  .hide-mobile {
    display: none !important;
  }
  
  .show-mobile {
    display: block !important;
  }
}

@media (min-width: 769px) {
  .hide-desktop {
    display: none !important;
  }
  
  .show-desktop {
    display: block !important;
  }
}
