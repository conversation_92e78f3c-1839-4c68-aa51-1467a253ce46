<template>
  <div class="article-container">
    <div class="top-actions">
      <el-button type="primary" @click="handleAddArticle">
        <el-icon><Plus /></el-icon>添加文章
      </el-button>
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          placeholder="搜索文章标题/内容"
          clearable
          @clear="fetchArticleList"
          @input="handleSearchInput"
        >
          <template #append>
            <el-button @click="fetchArticleList">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
    </div>
    
    <el-table v-loading="loading" :data="articleList" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip />
      <el-table-column prop="category" label="分类" width="100">
        <template #default="scope">
          <el-tag>{{ scope.row.category }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="author" label="作者" width="100" />
      <el-table-column prop="publishTime" label="发布时间" width="180" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 1" type="success">已发布</el-tag>
          <el-tag v-else type="info">草稿</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="viewCount" label="阅读量" width="100" />
      <el-table-column label="操作" width="240">
        <template #default="scope">
          <el-button size="small" @click="handleViewArticle(scope.row)">查看</el-button>
          <el-button size="small" type="primary" @click="handleEditArticle(scope.row)">编辑</el-button>
          <el-button 
            size="small" 
            :type="scope.row.status === 1 ? 'warning' : 'success'"
            @click="handleToggleStatus(scope.row)"
          >
            {{ scope.row.status === 1 ? '下架' : '发布' }}
          </el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDeleteArticle(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 文章编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑文章' : '添加文章'" 
      width="80%" 
      :destroy-on-close="true"
    >
      <el-form
        ref="articleFormRef"
        :model="articleForm"
        :rules="articleRules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="articleForm.title" placeholder="请输入文章标题" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="articleForm.category" placeholder="请选择文章分类">
            <el-option label="通知" value="通知" />
            <el-option label="新闻" value="新闻" />
            <el-option label="技术" value="技术" />
            <el-option label="公告" value="公告" />
            <el-option label="行情" value="行情" />
          </el-select>
        </el-form-item>
        <el-form-item label="摘要" prop="summary">
          <el-input
            v-model="articleForm.summary"
            type="textarea"
            :rows="3"
            placeholder="请输入文章摘要"
          />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <!-- 这里实际项目中应该使用富文本编辑器，如 wangEditor, CKEditor 等 -->
          <el-input
            v-model="articleForm.content"
            type="textarea"
            :rows="10"
            placeholder="请输入文章内容"
          />
        </el-form-item>
        <el-form-item label="作者" prop="author">
          <el-input v-model="articleForm.author" placeholder="请输入作者" style="width: 200px;" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="articleForm.status">
            <el-radio :label="1">发布</el-radio>
            <el-radio :label="0">草稿</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 文章详情对话框 -->
    <el-dialog 
      v-model="viewDialogVisible" 
      title="文章详情" 
      width="80%"
    >
      <div class="article-detail">
        <h2 class="article-title">{{ currentArticle.title }}</h2>
        <div class="article-meta">
          <span>作者: {{ currentArticle.author }}</span>
          <span>分类: {{ currentArticle.category }}</span>
          <span>发布时间: {{ currentArticle.publishTime }}</span>
          <span>阅读量: {{ currentArticle.viewCount }}</span>
        </div>
        <div class="article-summary">
          <strong>摘要:</strong> {{ currentArticle.summary }}
        </div>
        <div class="article-content">
          <div v-html="currentArticle.content"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'

// 表格数据
const loading = ref(false)
const articleList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchQuery = ref('')

// 对话框控制
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const isEdit = ref(false)
const articleFormRef = ref<FormInstance>()
const currentArticle = ref({
  id: 0,
  title: '',
  category: '',
  author: '',
  publishTime: '',
  status: 0,
  viewCount: 0,
  summary: '',
  content: ''
})

// 表单数据
const articleForm = reactive({
  id: 0,
  title: '',
  category: '',
  summary: '',
  content: '',
  author: '',
  status: 1
})

// 表单验证规则
const articleRules = reactive({
  title: [{ required: true, message: '请输入文章标题', trigger: 'blur' }],
  category: [{ required: true, message: '请选择文章分类', trigger: 'change' }],
  content: [{ required: true, message: '请输入文章内容', trigger: 'blur' }],
  author: [{ required: true, message: '请输入作者', trigger: 'blur' }]
})

// 获取文章列表
const fetchArticleList = () => {
  loading.value = true
  // 模拟API请求
  setTimeout(() => {
    articleList.value = [
      {
        id: 1,
        title: '关于新疆棉花质量控制的通知',
        category: '通知',
        author: '管理员',
        publishTime: '2023-07-20 15:30:00',
        status: 1,
        viewCount: 256,
        summary: '为了提高棉花质量，特发布本通知...',
        content: '<p>为了提高新疆棉花质量，特发布本通知：</p><p>1. 各仓库负责人必须严格执行质量检测流程；</p><p>2. 收购棉花时必须进行抽样检测；</p><p>3. 不符合标准的棉花一律不得入库；</p><p>4. 对于优质棉花可给予价格上的奖励。</p>'
      },
      {
        id: 2,
        title: '2023年第三季度棉花收购计划',
        category: '计划',
        author: '张三',
        publishTime: '2023-07-18 09:15:00',
        status: 1,
        viewCount: 189,
        summary: '2023年第三季度棉花收购计划已制定完成...',
        content: '<p>2023年第三季度棉花收购计划已制定完成，具体如下：</p><p>1. 计划收购长绒棉5000吨；</p><p>2. 计划收购短绒棉8000吨；</p><p>3. 收购时间为8月15日至10月30日；</p><p>4. 各分仓收购配额另行通知。</p>'
      },
      {
        id: 3,
        title: '棉花质量检测标准更新',
        category: '标准',
        author: '李四',
        publishTime: '2023-07-15 11:42:00',
        status: 1,
        viewCount: 145,
        summary: '为适应市场需求，我公司棉花质量检测标准已更新...',
        content: '<p>为适应市场需求，我公司棉花质量检测标准已更新，主要变更如下：</p><p>1. 纤维长度标准提高0.5mm；</p><p>2. 杂质含量要求降低至0.8%以下；</p><p>3. 强力值标准提高至29cN/tex以上；</p><p>4. 新增色泽均匀度检测项目。</p>'
      },
      {
        id: 4,
        title: '高品质棉花种植技术指南',
        category: '技术',
        author: '王五',
        publishTime: '2023-07-10 14:05:00',
        status: 0,
        viewCount: 0,
        summary: '本指南介绍了提高棉花品质的先进种植技术...',
        content: '<p>本指南介绍了提高棉花品质的先进种植技术，包括：</p><p>1. 优质品种选择与育苗技术；</p><p>2. 科学施肥与水分管理；</p><p>3. 病虫害综合防治方法；</p><p>4. 棉花采收与初加工技术要点。</p>'
      }
    ]
    total.value = 4
    loading.value = false
  }, 300)
}

// 处理搜索输入
const handleSearchInput = () => {
  // 防抖处理，实际项目中建议使用lodash的debounce
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchArticleList()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchArticleList()
}

// 添加文章
const handleAddArticle = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑文章
const handleEditArticle = (row: any) => {
  isEdit.value = true
  Object.assign(articleForm, row)
  dialogVisible.value = true
}

// 查看文章
const handleViewArticle = (row: any) => {
  currentArticle.value = row
  viewDialogVisible.value = true
}

// 切换文章状态
const handleToggleStatus = (row: any) => {
  const newStatus = row.status === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '发布' : '下架'
  
  ElMessageBox.confirm(
    `确定要${statusText}该文章吗?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 模拟API更新
    row.status = newStatus
    ElMessage({
      type: 'success',
      message: `${statusText}成功`
    })
  }).catch(() => {})
}

// 删除文章
const handleDeleteArticle = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除文章 "${row.title}" 吗?`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 模拟API删除
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    fetchArticleList()
  }).catch(() => {})
}

// 重置表单
const resetForm = () => {
  articleForm.id = 0
  articleForm.title = ''
  articleForm.category = ''
  articleForm.summary = ''
  articleForm.content = ''
  articleForm.author = ''
  articleForm.status = 1
}

// 提交表单
const submitForm = async () => {
  if (!articleFormRef.value) return
  
  await articleFormRef.value.validate((valid) => {
    if (valid) {
      // 模拟API提交
      ElMessage({
        type: 'success',
        message: isEdit.value ? '修改成功' : '添加成功'
      })
      dialogVisible.value = false
      fetchArticleList()
    }
  })
}

// 页面加载时获取文章列表
onMounted(() => {
  fetchArticleList()
})
</script>

<style scoped>
.article-container {
  padding: 20px 0;
}

.top-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-box {
  width: 320px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.article-detail {
  padding: 0 20px;
}

.article-title {
  text-align: center;
  margin-bottom: 20px;
  font-size: 24px;
  color: #303133;
}

.article-meta {
  display: flex;
  justify-content: center;
  color: #909399;
  font-size: 14px;
  margin-bottom: 20px;
}

.article-meta span {
  margin: 0 10px;
}

.article-summary {
  background-color: #f5f7fa;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  color: #606266;
}

.article-content {
  line-height: 1.8;
  color: #303133;
}
</style> 