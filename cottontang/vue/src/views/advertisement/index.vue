<template>
  <div class="adv-container">
    <div class="top-actions">
      <el-button type="primary" @click="handleAddAdv">
        <el-icon><Plus /></el-icon>添加广告
      </el-button>
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          placeholder="搜索广告标题"
          clearable
          @clear="fetchAdvList"
          @input="handleSearchInput"
        >
          <template #append>
            <el-button @click="fetchAdvList">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
    </div>
    
    <el-table v-loading="loading" :data="advList" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="title" label="标题" min-width="180" />
      <el-table-column prop="position" label="位置" width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.position === 'home_banner'" type="primary">首页轮播</el-tag>
          <el-tag v-else-if="scope.row.position === 'home_sidebar'" type="success">首页侧栏</el-tag>
          <el-tag v-else-if="scope.row.position === 'category'" type="warning">分类页</el-tag>
          <el-tag v-else type="info">其他</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="图片" width="150">
        <template #default="scope">
          <el-image
            style="width: 100px; height: 50px"
            :src="scope.row.imageUrl"
            :preview-src-list="[scope.row.imageUrl]"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column prop="sort" label="排序" width="80" />
      <el-table-column prop="startTime" label="开始时间" width="180" />
      <el-table-column prop="endTime" label="结束时间" width="180" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 1" type="success">启用</el-tag>
          <el-tag v-else type="info">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button size="small" @click="handlePreviewAdv(scope.row)">预览</el-button>
          <el-button size="small" type="primary" @click="handleEditAdv(scope.row)">编辑</el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDeleteAdv(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 广告编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑广告' : '添加广告'" 
      width="600px" 
      :destroy-on-close="true"
    >
      <el-form
        ref="advFormRef"
        :model="advForm"
        :rules="advRules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="advForm.title" placeholder="请输入广告标题" />
        </el-form-item>
        <el-form-item label="位置" prop="position">
          <el-select v-model="advForm.position" placeholder="请选择广告位置">
            <el-option label="首页轮播" value="home_banner" />
            <el-option label="首页侧栏" value="home_sidebar" />
            <el-option label="分类页" value="category" />
            <el-option label="其他位置" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="链接地址" prop="url">
          <el-input v-model="advForm.url" placeholder="请输入链接地址" />
        </el-form-item>
        <el-form-item label="广告图片" prop="imageUrl">
          <!-- 实际项目中应该有图片上传功能 -->
          <el-input v-model="advForm.imageUrl" placeholder="请输入图片URL">
            <template #append>
              <el-button @click="handleImageSelect">选择</el-button>
            </template>
          </el-input>
          <div class="image-preview" v-if="advForm.imageUrl">
            <el-image :src="advForm.imageUrl" style="width: 200px; height: 100px" fit="cover" />
          </div>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="advForm.sort" :min="0" :max="99" />
        </el-form-item>
        <el-form-item label="时间范围" prop="timeRange">
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            :shortcuts="dateShortcuts"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="advForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 广告预览对话框 -->
    <el-dialog 
      v-model="previewDialogVisible" 
      title="广告预览" 
      width="800px"
      center
    >
      <div class="adv-preview">
        <h3>{{ currentAdv.title }}</h3>
        <div class="adv-image">
          <el-image :src="currentAdv.imageUrl" fit="contain" style="width: 100%" />
        </div>
        <div class="adv-info">
          <p><strong>位置：</strong> 
            <el-tag v-if="currentAdv.position === 'home_banner'" type="primary">首页轮播</el-tag>
            <el-tag v-else-if="currentAdv.position === 'home_sidebar'" type="success">首页侧栏</el-tag>
            <el-tag v-else-if="currentAdv.position === 'category'" type="warning">分类页</el-tag>
            <el-tag v-else type="info">其他</el-tag>
          </p>
          <p><strong>链接：</strong> <a :href="currentAdv.url" target="_blank">{{ currentAdv.url }}</a></p>
          <p><strong>时间范围：</strong> {{ currentAdv.startTime }} 至 {{ currentAdv.endTime }}</p>
          <p><strong>状态：</strong> 
            <el-tag v-if="currentAdv.status === 1" type="success">启用</el-tag>
            <el-tag v-else type="info">禁用</el-tag>
          </p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'

// 表格数据
const loading = ref(false)
const advList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchQuery = ref('')

// 对话框控制
const dialogVisible = ref(false)
const previewDialogVisible = ref(false)
const isEdit = ref(false)
const advFormRef = ref<FormInstance>()
const currentAdv = ref({
  id: 0,
  title: '',
  position: '',
  imageUrl: '',
  url: '',
  sort: 0,
  startTime: '',
  endTime: '',
  status: 1
})

// 表单数据
const advForm = reactive({
  id: 0,
  title: '',
  position: '',
  imageUrl: '',
  url: '',
  sort: 0,
  startTime: '',
  endTime: '',
  status: 1
})

// 时间范围
const timeRange = ref<string[]>([])

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '未来一个月',
    value: () => {
      const start = new Date()
      const end = new Date()
      end.setTime(end.getTime() + 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  }
]

// 表单验证规则
const advRules = reactive({
  title: [{ required: true, message: '请输入广告标题', trigger: 'blur' }],
  position: [{ required: true, message: '请选择广告位置', trigger: 'change' }],
  imageUrl: [{ required: true, message: '请输入图片URL', trigger: 'blur' }],
  url: [{ required: true, message: '请输入链接地址', trigger: 'blur' }]
})

// 监听时间范围变化
watch(timeRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    advForm.startTime = newValue[0]
    advForm.endTime = newValue[1]
  } else {
    advForm.startTime = ''
    advForm.endTime = ''
  }
}, { deep: true })

// 获取广告列表
const fetchAdvList = () => {
  loading.value = true
  // 模拟API请求
  setTimeout(() => {
    advList.value = [
      {
        id: 1,
        title: '新疆优质棉花促销',
        position: 'home_banner',
        imageUrl: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
        url: 'https://example.com/promo1',
        sort: 0,
        startTime: '2023-07-01 00:00:00',
        endTime: '2023-08-31 23:59:59',
        status: 1
      },
      {
        id: 2,
        title: '棉花收购季',
        position: 'home_sidebar',
        imageUrl: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
        url: 'https://example.com/season',
        sort: 1,
        startTime: '2023-07-15 00:00:00',
        endTime: '2023-09-15 23:59:59',
        status: 1
      },
      {
        id: 3,
        title: '棉花质量标准',
        position: 'category',
        imageUrl: 'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
        url: 'https://example.com/standard',
        sort: 2,
        startTime: '2023-06-01 00:00:00',
        endTime: '2023-12-31 23:59:59',
        status: 1
      },
      {
        id: 4,
        title: '会员专享优惠',
        position: 'other',
        imageUrl: 'https://shadow.elemecdn.com/app/element/hamburger.9cf7b091-55e9-11e9-a976-7f4d0b07eef6.png',
        url: 'https://example.com/member',
        sort: 3,
        startTime: '2023-07-01 00:00:00',
        endTime: '2023-07-31 23:59:59',
        status: 0
      }
    ]
    total.value = 4
    loading.value = false
  }, 300)
}

// 处理搜索输入
const handleSearchInput = () => {
  // 防抖处理，实际项目中建议使用lodash的debounce
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchAdvList()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchAdvList()
}

// 添加广告
const handleAddAdv = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑广告
const handleEditAdv = (row: any) => {
  isEdit.value = true
  Object.assign(advForm, row)
  timeRange.value = [row.startTime, row.endTime]
  dialogVisible.value = true
}

// 预览广告
const handlePreviewAdv = (row: any) => {
  currentAdv.value = row
  previewDialogVisible.value = true
}

// 删除广告
const handleDeleteAdv = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除广告 "${row.title}" 吗?`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 模拟API删除
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    fetchAdvList()
  }).catch(() => {})
}

// 图片选择
const handleImageSelect = () => {
  // 实际项目中应该打开图片选择器
  ElMessage({
    type: 'info',
    message: '请实现图片上传功能'
  })
}

// 重置表单
const resetForm = () => {
  advForm.id = 0
  advForm.title = ''
  advForm.position = ''
  advForm.imageUrl = ''
  advForm.url = ''
  advForm.sort = 0
  advForm.startTime = ''
  advForm.endTime = ''
  advForm.status = 1
  timeRange.value = []
}

// 提交表单
const submitForm = async () => {
  if (!advFormRef.value) return
  
  await advFormRef.value.validate((valid) => {
    if (valid) {
      // 模拟API提交
      ElMessage({
        type: 'success',
        message: isEdit.value ? '修改成功' : '添加成功'
      })
      dialogVisible.value = false
      fetchAdvList()
    }
  })
}

// 页面加载时获取广告列表
onMounted(() => {
  fetchAdvList()
})
</script>

<style scoped>
.adv-container {
  padding: 20px 0;
}

.top-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-box {
  width: 320px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.image-preview {
  margin-top: 10px;
}

.adv-preview {
  text-align: center;
}

.adv-preview h3 {
  margin-bottom: 20px;
}

.adv-image {
  margin-bottom: 20px;
}

.adv-info {
  text-align: left;
  padding: 0 50px;
}

.adv-info p {
  margin: 10px 0;
}
</style> 