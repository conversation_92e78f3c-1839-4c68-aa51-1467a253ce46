<template>
  <div class="login-page bg_f2">
    <!-- 顶部导航 -->
    <div class="top-nav min_w">
      <div class="m_auto clearfix">
        <div class="logo fl">
          <h1 class="logo-text">棉花糖交易平台</h1>
        </div>
      </div>
    </div>

    <!-- 登录区域 -->
    <div class="login_bg min_w">
      <div class="m_auto clearfix">
        <div class="login_box bg_f fr mt_70">
          <div class="login_box_tt f_18 padl_30">用户登录</div>
          <div class="padl_40">
            <form @submit.prevent="handleLogin">
              <div class="border1 pad_10 mt_30">
                <el-icon class="ver_mid input-icon">
                  <User/>
                </el-icon>
                <input
                    v-model="loginForm.username"
                    placeholder="手机号"
                    class="f_16 clr_9 border0 ver_mid ml_10"
                    maxlength="11"
                />
              </div>

              <div class="border1 pad_10 mt_30">
                <el-icon class="ver_mid input-icon">
                  <Lock/>
                </el-icon>
                <input
                    v-model="loginForm.password"
                    placeholder="密码"
                    type="password"
                    class="f_16 clr_9 border0 ver_mid ml_10"
                />
              </div>

              <div class="padt_30">
                <input
                    type="submit"
                    value="登录"
                    :disabled="loading"
                    class="f_16 clr_f bg_b w_100p border0 padt_10 login-submit-btn"
                />

                <div class="f_14" style="margin-top:8px; float:left;">
                  <label>
                    <input
                        type="checkbox"
                        v-model="loginForm.remember"
                        class="ver_mid"
                        value="1"
                    >
                    <span class="ver_mid">记住密码</span>
                  </label>
                </div>

                <div class="f_14 al_rt mt_10">
                  <a href="#" class="clr_b">忘记密码？</a>
                  <router-link to="/register" class="clr_3">新用户注册</router-link>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部 -->
    <div class="min_w">
      <div class="footer">
        <div class="m_auto">
          <p class="al_ct clr_f padt_30">© 2024 棉花糖交易平台 版权所有</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import type {FormInstance, FormRules} from 'element-plus'
import {ElMessage} from 'element-plus'
import {Lock, User} from '@element-plus/icons-vue'
import {login} from '@/api/auth'
import {useUserStore} from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()
const loginFormRef = ref<FormInstance>()
const loading = ref(false)

// Login form data
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// Form validation rules
const loginRules = reactive<FormRules>({
  username: [
    {required: true, message: '请输入手机号', trigger: 'blur'},
    {pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur'}
  ],
  password: [{required: true, message: '请输入密码', trigger: 'blur'}],
})

// Login handler
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    loading.value = true

    const response = await login({
      username: loginForm.username,
      password: loginForm.password,
      remember: loginForm.remember
    })

    // 存储token和用户信息
    userStore.setToken(response.data.token)
    userStore.setUserInfo(response.data.userInfo)

    // 记住密码
    if (loginForm.remember) {
      localStorage.setItem('remembered_username', loginForm.username)
    } else {
      localStorage.removeItem('remembered_username')
    }

    ElMessage.success('登录成功')

    // 跳转到首页
    router.push('/public/home')
  } catch (error: any) {
    ElMessage.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}

// 页面加载时恢复记住的用户名
onMounted(() => {
  const rememberedUsername = localStorage.getItem('remembered_username')
  if (rememberedUsername) {
    loginForm.username = rememberedUsername
    loginForm.remember = true
  }
})
</script>

<style scoped>
/* 基础样式 - 完全按照PHP的CSS */
.bg_f2 {
  background: #e4e5e6;
}

.bg_f {
  background: #FFF;
}

.bg_b {
  background: #28afe5;
}

.clr_f {
  color: #fff;
}

.clr_9 {
  color: #999;
}

.clr_b {
  color: #28afe5;
}

.clr_3 {
  color: #333;
  margin-right: 10px;
}

.f_16 {
  font-size: 16px;
}

.f_18 {
  font-size: 18px;
}

.f_14 {
  font-size: 14px;
}

.border1 {
  border: 1px solid #e4e4e4;
}

.border0 {
  border: 0;
}

.pad_10 {
  padding: 10px;
}

.padt_10 {
  padding: 10px 0;
}

.padt_30 {
  padding: 30px 0;
}

.padl_30 {
  padding: 0 30px;
}

.padl_40 {
  padding: 0 40px;
}

.mt_30 {
  margin-top: 30px;
}

.mt_70 {
  margin-top: 70px;
}

.mt_10 {
  margin-top: 10px;
}

.ml_10 {
  margin-left: 10px;
}

.ver_mid {
  vertical-align: middle;
}

.al_rt {
  text-align: right;
}

.al_ct {
  text-align: center;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.w_100p {
  width: 100%;
}

.min_w {
  min-width: 1200px;
}

.m_auto {
  min-width: 1200px;
  width: 1200px;
  margin: 0 auto;
}

.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

/* 页面整体 */
.login-page {
  min-height: 100vh;
}

/* 顶部导航 */
.top-nav {
  background: #fff;
  height: 80px;
  line-height: 80px;
}

.logo-text {
  margin: 0;
  color: #28afe5;
  font-size: 24px;
  font-weight: bold;
  line-height: 80px;
}

/* 登录背景 */
.login_bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 553px;
  background-size: cover;
  background-position: center;
}

/* 登录框 */
.login_box {
  width: 380px;
  background: #fff;
}

.login_box_tt {
  height: 60px;
  background: #eee;
  line-height: 60px;
}

/* 输入框样式 */
.login_box input[type="text"],
.login_box input[type="password"],
.login_box input:not([type="submit"]):not([type="checkbox"]) {
  width: 280px;
  outline: none;
  background: transparent;
}

.login_box input[type="text"]:focus,
.login_box input[type="password"]:focus {
  outline: none;
}

/* 图标样式 */
.input-icon {
  color: #999;
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* 登录按钮 */
.login-submit-btn {
  height: 45px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.login-submit-btn:hover:not(:disabled) {
  background: #1182c4 !important;
}

.login-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 链接样式 */
.clr_b {
  color: #28afe5;
  text-decoration: none;
}

.clr_b:hover {
  text-decoration: underline;
}

.clr_3 {
  color: #333;
  margin-right: 10px;
  text-decoration: none;
}

.clr_3:hover {
  color: #28afe5;
  text-decoration: underline;
}

/* 复选框样式 */
input[type="checkbox"] {
  margin-right: 5px;
}

/* 底部 */
.footer {
  background: #121212;
  min-height: 100px;
}

/* 响应式 */
@media (max-width: 1200px) {
  .min_w {
    min-width: 100%;
  }

  .m_auto {
    min-width: 100%;
    width: 100%;
    padding: 0 20px;
  }

  .login_box {
    width: 100%;
    max-width: 380px;
    margin: 0 auto;
  }

  .login_bg {
    height: auto;
    min-height: 553px;
    padding: 50px 0;
  }
}

@media (max-width: 768px) {
  .login_box input:not([type="submit"]):not([type="checkbox"]) {
    width: 200px;
  }

  .padl_40 {
    padding: 0 20px;
  }

  .padl_30 {
    padding: 0 20px;
  }
}
</style>
