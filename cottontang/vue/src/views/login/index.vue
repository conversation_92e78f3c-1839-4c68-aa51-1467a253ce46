<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-title">
        <h2>棉花糖交易平台</h2>
        <p>请登录您的账户</p>
      </div>
      <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form">
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            prefix-icon="User"
            placeholder="请输入手机号"
            maxlength="11"
          />
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            prefix-icon="Lock"
            type="password"
            placeholder="请输入密码"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleLogin" class="login-button">
            登 录
          </el-button>
        </el-form-item>
        <div class="login-links">
          <router-link to="/register" class="register-link">立即注册</router-link>
          <a href="#" class="forgot-link">忘记密码？</a>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { login } from '@/api/auth'
import { useUserStore } from '@/stores/user'
import type { FormInstance, FormRules } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()
const loginFormRef = ref<FormInstance>()
const loading = ref(false)

// Login form data
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// Form validation rules
const loginRules = reactive<FormRules>({
  username: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
})

// Login handler
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    loading.value = true

    const response = await login({
      username: loginForm.username,
      password: loginForm.password,
      remember: loginForm.remember
    })

    // 存储token和用户信息
    userStore.setToken(response.data.token)
    userStore.setUserInfo(response.data.userInfo)

    // 记住密码
    if (loginForm.remember) {
      localStorage.setItem('remembered_username', loginForm.username)
    } else {
      localStorage.removeItem('remembered_username')
    }

    ElMessage.success('登录成功')

    // 跳转到首页
    router.push('/public/home')
  } catch (error: any) {
    ElMessage.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}

// 页面加载时恢复记住的用户名
onMounted(() => {
  const rememberedUsername = localStorage.getItem('remembered_username')
  if (rememberedUsername) {
    loginForm.username = rememberedUsername
    loginForm.remember = true
  }
})
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-box {
  width: 100%;
  max-width: 400px;
  padding: 40px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.login-title {
  margin-bottom: 30px;
  text-align: center;
}

.login-title h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 28px;
}

.login-title p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.login-button {
  width: 100%;
  height: 45px;
  font-size: 16px;
}

.login-links {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.register-link,
.forgot-link {
  color: #409EFF;
  text-decoration: none;
  font-size: 14px;
}

.register-link:hover,
.forgot-link:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .login-box {
    padding: 30px 20px;
  }

  .login-links {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}
</style>