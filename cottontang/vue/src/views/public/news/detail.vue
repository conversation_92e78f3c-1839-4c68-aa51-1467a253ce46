<template>
  <div class="news-detail-page">
    <div class="container">
      <!-- 返回按钮 -->
      <div class="back-button">
        <el-button @click="goBack" :icon="ArrowLeft">返回新闻列表</el-button>
      </div>

      <!-- 新闻详情 -->
      <div v-loading="loading" class="news-detail">
        <div v-if="newsDetail" class="news-content">
          <!-- 新闻头部 -->
          <div class="news-header">
            <div class="news-category">
              {{ getCategoryName(newsDetail.category) }}
            </div>
            <h1 class="news-title">{{ newsDetail.title }}</h1>
            
            <div class="news-meta">
              <div class="meta-item">
                <span class="meta-label">发布时间：</span>
                <span class="meta-value">{{ formatDateTime(newsDetail.publishTime || newsDetail.addTime) }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">阅读量：</span>
                <span class="meta-value">{{ newsDetail.viewCount || 0 }}</span>
              </div>
              <div v-if="newsDetail.author" class="meta-item">
                <span class="meta-label">作者：</span>
                <span class="meta-value">{{ newsDetail.author }}</span>
              </div>
            </div>
          </div>

          <!-- 新闻图片 -->
          <div v-if="newsDetail.imageUrl" class="news-image">
            <img :src="newsDetail.imageUrl" :alt="newsDetail.title" />
          </div>

          <!-- 新闻摘要 -->
          <div v-if="newsDetail.summary" class="news-summary">
            <p>{{ newsDetail.summary }}</p>
          </div>

          <!-- 新闻正文 -->
          <div class="news-body" v-html="newsDetail.content || newsDetail.description"></div>

          <!-- 标签 -->
          <div v-if="newsDetail.tags && newsDetail.tags.length > 0" class="news-tags">
            <span class="tags-label">标签：</span>
            <el-tag
              v-for="tag in newsDetail.tags"
              :key="tag"
              class="news-tag"
              type="primary"
              effect="plain"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>

        <!-- 加载失败状态 -->
        <div v-else-if="!loading" class="error-state">
          <el-result
            icon="warning"
            title="新闻不存在"
            sub-title="您访问的新闻可能已被删除或不存在"
          >
            <template #extra>
              <el-button type="primary" @click="goBack">返回新闻列表</el-button>
            </template>
          </el-result>
        </div>
      </div>

      <!-- 相关新闻 -->
      <div v-if="relatedNews.length > 0" class="related-news">
        <h3>相关新闻</h3>
        <div class="related-news-list">
          <div
            v-for="news in relatedNews"
            :key="news.id"
            class="related-news-item"
            @click="viewNews(news.id)"
          >
            <div class="related-news-image">
              <img
                :src="news.imageUrl || 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'"
                :alt="news.title"
              />
            </div>
            <div class="related-news-content">
              <h4>{{ news.title }}</h4>
              <p>{{ news.summary || news.description }}</p>
              <span class="related-news-date">{{ formatDate(news.publishTime || news.addTime) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getNewsById, getNewsList } from '@/api/public'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const newsDetail = ref(null)
const relatedNews = ref([])

// 获取新闻详情
const fetchNewsDetail = async () => {
  const newsId = route.params.id as string
  if (!newsId) {
    ElMessage.error('新闻ID无效')
    goBack()
    return
  }

  loading.value = true
  try {
    const response = await getNewsById(newsId)
    newsDetail.value = response.data
    
    // 设置页面标题
    if (newsDetail.value) {
      document.title = `${newsDetail.value.title} - 棉花糖`
      
      // 获取相关新闻
      fetchRelatedNews(newsDetail.value.category)
    }
  } catch (error) {
    ElMessage.error('获取新闻详情失败')
    newsDetail.value = null
  } finally {
    loading.value = false
  }
}

// 获取相关新闻
const fetchRelatedNews = async (category: string) => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 4,
      category: category
    }
    
    const response = await getNewsList(params)
    const allNews = response.data.records || []
    
    // 排除当前新闻
    const currentNewsId = parseInt(route.params.id as string)
    relatedNews.value = allNews.filter((news: any) => news.id !== currentNewsId)
  } catch (error) {
    console.error('获取相关新闻失败:', error)
  }
}

// 返回新闻列表
const goBack = () => {
  router.push('/public/news')
}

// 查看其他新闻
const viewNews = (id: number) => {
  router.push(`/public/news/${id}`)
  // 重新加载当前新闻详情
  fetchNewsDetail()
}

// 获取分类名称
const getCategoryName = (category: string) => {
  const categoryMap: Record<string, string> = {
    company: '公司新闻',
    industry: '行业资讯',
    product: '产品动态',
    market: '市场分析'
  }
  return categoryMap[category] || '其他'
}

// 格式化日期时间
const formatDateTime = (timestamp: number) => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化日期
const formatDate = (timestamp: number) => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 页面加载时获取数据
onMounted(() => {
  fetchNewsDetail()
})
</script>

<style scoped>
.news-detail-page {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 2rem 0;
}

.container {
  width: 1200px;
  max-width: 100%;
  padding: 0 15px;
  margin: 0 auto;
}

.back-button {
  margin-bottom: 2rem;
}

.news-detail {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.news-content {
  padding: 3rem;
}

.news-header {
  margin-bottom: 2rem;
}

.news-category {
  display: inline-block;
  background: #667eea;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.news-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  line-height: 1.3;
  margin-bottom: 1.5rem;
}

.news-meta {
  display: flex;
  gap: 2rem;
  padding: 1rem 0;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.meta-item {
  display: flex;
  align-items: center;
}

.meta-label {
  color: #666;
  margin-right: 0.5rem;
}

.meta-value {
  color: #333;
  font-weight: 500;
}

.news-image {
  margin: 2rem 0;
  text-align: center;
}

.news-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.news-summary {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 2rem 0;
  border-left: 4px solid #667eea;
}

.news-summary p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
  margin: 0;
}

.news-body {
  font-size: 1rem;
  line-height: 1.8;
  color: #333;
  margin: 2rem 0;
}

.news-body :deep(p) {
  margin-bottom: 1rem;
}

.news-body :deep(img) {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
  border-radius: 4px;
}

.news-tags {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.tags-label {
  color: #666;
  margin-right: 1rem;
}

.news-tag {
  margin-right: 0.5rem;
}

.error-state {
  padding: 3rem;
  text-align: center;
}

.related-news {
  margin-top: 3rem;
  background: white;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.related-news h3 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #333;
}

.related-news-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.related-news-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.related-news-item:hover {
  background: #f8f9fa;
}

.related-news-image {
  flex-shrink: 0;
  width: 80px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
}

.related-news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.related-news-content {
  flex: 1;
}

.related-news-content h4 {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: #333;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-news-content p {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-news-date {
  font-size: 0.8rem;
  color: #999;
}

@media (max-width: 768px) {
  .news-content {
    padding: 2rem 1rem;
  }
  
  .news-title {
    font-size: 1.8rem;
  }
  
  .news-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .related-news-list {
    grid-template-columns: 1fr;
  }
}
</style>
