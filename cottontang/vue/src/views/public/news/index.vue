<template>
  <div class="news-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="container">
        <h1>新闻动态</h1>
        <p>了解棉花糖最新动态和行业资讯</p>
      </div>
    </div>

    <!-- 新闻列表 -->
    <div class="news-content">
      <div class="container">
        <div class="news-filter">
          <el-input
            v-model="searchQuery"
            placeholder="搜索新闻..."
            :prefix-icon="Search"
            @input="handleSearch"
            clearable
            class="search-input"
          />
          <el-select
            v-model="selectedCategory"
            placeholder="选择分类"
            @change="handleCategoryChange"
            clearable
            class="category-select"
          >
            <el-option label="全部" value="" />
            <el-option label="公司新闻" value="company" />
            <el-option label="行业资讯" value="industry" />
            <el-option label="产品动态" value="product" />
            <el-option label="市场分析" value="market" />
          </el-select>
        </div>

        <div v-loading="loading" class="news-list">
          <div v-if="newsList.length === 0 && !loading" class="empty-state">
            <p>暂无新闻数据</p>
          </div>
          
          <div v-else class="news-grid">
            <div
              v-for="news in newsList"
              :key="news.id"
              class="news-card"
              @click="viewNewsDetail(news.id)"
            >
              <div class="news-image">
                <img
                  :src="news.imageUrl || 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'"
                  :alt="news.title"
                />
                <div class="news-category">
                  {{ getCategoryName(news.category) }}
                </div>
              </div>
              
              <div class="news-content">
                <h3 class="news-title">{{ news.title }}</h3>
                <p class="news-summary">{{ news.summary || news.description }}</p>
                
                <div class="news-meta">
                  <span class="news-date">{{ formatDate(news.publishTime || news.addTime) }}</span>
                  <span class="news-views">{{ news.viewCount || 0 }} 次阅读</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[12, 24, 48]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getNewsList } from '@/api/public'

// 页面元数据
document.title = '新闻动态 - 棉花糖'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const newsList = ref([])
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)
const searchQuery = ref('')
const selectedCategory = ref('')

// 获取新闻列表
const fetchNewsList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchQuery.value,
      category: selectedCategory.value
    }
    
    const response = await getNewsList(params)
    newsList.value = response.data.records || []
    total.value = response.data.total || 0
  } catch (error) {
    ElMessage.error('获取新闻列表失败')
    newsList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchNewsList()
}

// 分类变更处理
const handleCategoryChange = () => {
  currentPage.value = 1
  fetchNewsList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchNewsList()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchNewsList()
}

// 查看新闻详情
const viewNewsDetail = (id: number) => {
  router.push(`/public/news/${id}`)
}

// 获取分类名称
const getCategoryName = (category: string) => {
  const categoryMap: Record<string, string> = {
    company: '公司新闻',
    industry: '行业资讯',
    product: '产品动态',
    market: '市场分析'
  }
  return categoryMap[category] || '其他'
}

// 格式化日期
const formatDate = (timestamp: number) => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 页面加载时获取数据
onMounted(() => {
  fetchNewsList()
})
</script>

<style scoped>
.news-page {
  min-height: 100vh;
}

.container {
  width: 1200px;
  max-width: 100%;
  padding: 0 15px;
  margin: 0 auto;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.page-header h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.page-header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.news-content {
  padding: 60px 0;
}

.news-filter {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
  justify-content: center;
}

.search-input {
  width: 300px;
}

.category-select {
  width: 150px;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.news-card {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.news-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.news-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-card:hover .news-image img {
  transform: scale(1.05);
}

.news-category {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(102, 126, 234, 0.9);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
}

.news-content {
  padding: 1.5rem;
}

.news-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-summary {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: #999;
}

.empty-state {
  text-align: center;
  padding: 4rem 0;
  color: #999;
}

.empty-state p {
  font-size: 1.1rem;
}

.pagination-container {
  margin-top: 3rem;
  text-align: center;
}

@media (max-width: 768px) {
  .news-filter {
    flex-direction: column;
    align-items: center;
  }
  
  .search-input,
  .category-select {
    width: 100%;
    max-width: 300px;
  }
  
  .news-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
}
</style>
