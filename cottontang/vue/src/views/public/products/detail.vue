<template>
  <div class="product-detail-page">
    <div class="container">
      <div class="product-detail" v-loading="loading">
        <div v-if="product" class="product-content">
          <div class="product-header">
            <h1>{{ product.leixing }}</h1>
            <div class="product-status">
              <el-tag :type="getStatusType(product.danType)" size="large">
                {{ product.danType }}
              </el-tag>
            </div>
          </div>

          <div class="product-info-grid">
            <div class="info-section">
              <h3>基本信息</h3>
              <div class="info-grid">
                <div class="info-item">
                  <label>序号:</label>
                  <span>{{ product.xvhao }}</span>
                </div>
                <div class="info-item">
                  <label>批号/捆号:</label>
                  <span>{{ product.pihaoKunkao }}</span>
                </div>
                <div class="info-item">
                  <label>类型:</label>
                  <span>{{ product.leixing }}</span>
                </div>
                <div class="info-item">
                  <label>产地:</label>
                  <span>{{ product.chandi }}</span>
                </div>
              </div>
            </div>

            <div class="info-section">
              <h3>质量指标</h3>
              <div class="info-grid">
                <div class="info-item">
                  <label>颜色级/品级:</label>
                  <span>{{ product.yansejiPinji }}</span>
                </div>
                <div class="info-item">
                  <label>马值:</label>
                  <span>{{ product.mazhi }}</span>
                </div>
                <div class="info-item">
                  <label>长度:</label>
                  <span>{{ product.changdu }}</span>
                </div>
                <div class="info-item">
                  <label>强力:</label>
                  <span>{{ product.qiangli }}</span>
                </div>
                <div class="info-item">
                  <label>含杂率:</label>
                  <span>{{ product.hanzalv }}</span>
                </div>
                <div class="info-item">
                  <label>回潮率:</label>
                  <span>{{ product.huichaolv }}</span>
                </div>
                <div class="info-item">
                  <label>整齐度:</label>
                  <span>{{ product.zhengqidu }}</span>
                </div>
              </div>
            </div>

            <div class="info-section">
              <h3>重量信息</h3>
              <div class="info-grid">
                <div class="info-item">
                  <label>公重(吨):</label>
                  <span>{{ product.gongzhong }}</span>
                </div>
                <div class="info-item">
                  <label>毛重(吨):</label>
                  <span>{{ product.maozhaong }}</span>
                </div>
                <div class="info-item">
                  <label>包数:</label>
                  <span>{{ product.baoshu }}</span>
                </div>
                <div class="info-item">
                  <label>白棉1/2/3级:</label>
                  <span>{{ product.bm123 }}</span>
                </div>
              </div>
            </div>

            <div class="info-section">
              <h3>仓储信息</h3>
              <div class="info-grid">
                <div class="info-item">
                  <label>加工厂:</label>
                  <span>{{ product.jiagongchang }}</span>
                </div>
                <div class="info-item">
                  <label>仓储名称:</label>
                  <span>{{ product.cangchumingcheng }}</span>
                </div>
                <div class="info-item">
                  <label>基差:</label>
                  <span>{{ product.jicha }}</span>
                </div>
                <div class="info-item">
                  <label>点价合约:</label>
                  <span>{{ product.dianjiaheyue }}</span>
                </div>
              </div>
            </div>

            <div class="info-section" v-if="product.beizhu">
              <h3>备注信息</h3>
              <p>{{ product.beizhu }}</p>
            </div>
          </div>

          <div class="product-actions">
            <el-button type="primary" size="large" @click="addToFavorite">
              <el-icon><Star /></el-icon>
              收藏
            </el-button>
            <el-button type="success" size="large" @click="createCustomOrder">
              <el-icon><Plus /></el-icon>
              定制订单
            </el-button>
            <el-button size="large" @click="goBack">
              <el-icon><ArrowLeft /></el-icon>
              返回列表
            </el-button>
          </div>
        </div>

        <div v-else class="not-found">
          <el-result icon="warning" title="产品不存在" sub-title="您访问的产品可能已被删除或不存在">
            <template #extra>
              <el-button type="primary" @click="goBack">返回列表</el-button>
            </template>
          </el-result>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Star, Plus, ArrowLeft } from '@element-plus/icons-vue'
import { getHomepageById, addFavorite } from '@/api/homepage'

const route = useRoute()
const router = useRouter()

const loading = ref(false)
const product = ref(null)

// 获取产品详情
const fetchProductDetail = async () => {
  loading.value = true
  try {
    const id = route.params.id as string
    const response = await getHomepageById(id)
    product.value = response.data
  } catch (error) {
    ElMessage.error('获取产品详情失败')
  } finally {
    loading.value = false
  }
}

// 添加收藏
const addToFavorite = async () => {
  try {
    await addFavorite(product.value.id, 1)
    ElMessage.success('收藏成功')
  } catch (error) {
    ElMessage.error('收藏失败，请先登录')
  }
}

// 创建定制订单
const createCustomOrder = () => {
  // 跳转到定制订单页面，传递产品信息
  router.push({
    path: '/public/custom-order',
    query: {
      productId: product.value.id,
      leixing: product.value.leixing,
      pihaoKunkao: product.value.pihaoKunkao
    }
  })
}

// 返回列表
const goBack = () => {
  router.back()
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '在售': return 'success'
    case '仓单': return 'info'
    case '挂单': return 'warning'
    case '已售': return 'danger'
    default: return ''
  }
}

onMounted(() => {
  fetchProductDetail()
})
</script>

<style scoped>
.product-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.product-content {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.product-header h1 {
  font-size: 2rem;
  color: #333;
  margin: 0;
}

.product-info-grid {
  display: grid;
  gap: 30px;
  margin-bottom: 30px;
}

.info-section h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.2rem;
  border-left: 4px solid #409eff;
  padding-left: 10px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: bold;
  color: #666;
  min-width: 120px;
  margin-right: 10px;
}

.info-item span {
  color: #333;
}

.product-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.product-actions .el-button {
  margin: 0 10px;
}

.not-found {
  text-align: center;
  padding: 50px;
}
</style>
