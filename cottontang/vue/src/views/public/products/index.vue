<template>
  <div class="bg_f2">
    <!-- 棉花商城 - 完全按照PHP原版 -->
    <div class="m_auto">
      <div class="bar">棉花商城</div>
      <div class="border1 pad_20 bg_f mt_10">
        <!-- 主分类导航 - 完全按照PHP原版 -->
        <ul class="mian_ul clearfix al_ct f_18">
          <li v-for="item in chandi" :key="item.id">
            <a 
              @click="selectMainCategory(item.id)" 
              :class="['clr_3', currentDef === item.id ? 'mian_cur' : 'mian_norm']"
            >
              {{ item.name }}
            </a>
          </li>
        </ul>

        <!-- 产地筛选 - 完全按照PHP原版 -->
        <ul class="chandi_ul f_14 mt_20 fl" style="line-height:30px;margin-left:5px;width:370px;">
          <li style="display: inline-block;" class="ver_top">产　地：</li>
          <li style="display: inline-block;">
            <div id="chandi_click">
              <a 
                v-for="item in chandiDefault" 
                :key="item.id"
                @click="toggleParentChandi(item)"
                :class="['clr_3', 'mr_10', selectedParentChandi.includes(item.id) ? 'xuan_cur' : '']"
                :myid="item.id"
              >
                {{ item.name }}
              </a>
            </div>
          </li>
        </ul>

        <!-- 类型筛选 - 完全按照PHP原版 -->
        <ul class="chandi_ul f_14 mt_20 fl" style="line-height:30px; margin-left:5px;width:375px;">
          <li>类　型：</li>
          <li v-for="item in xlsLeixing" :key="item">
            <a 
              @click="toggleLeixing(item)"
              :class="['clr_3', selectedLeixing.includes(item) ? 'xuan_cur' : '']"
            >
              {{ item }}
            </a>
          </li>
        </ul>

        <!-- 年度筛选 - 完全按照PHP原版 -->
        <ul class="chandi_ul f_14 mt_20 fl" style="line-height:30px; margin-left:5px; width:330px;">
          <li>年　度：</li>
          <li v-for="item in xlsNiandu" :key="item">
            <a 
              @click="toggleNiandu(item)"
              :class="['clr_3', selectedNiandu.includes(item) ? 'xuan_cur' : '']"
            >
              {{ item }}
            </a>
          </li>
        </ul>

        <div class="cls"></div>

        <!-- 子产地容器 - 完全按照PHP原版 -->
        <div id="child_wraper">
          <span v-for="parent in selectedParentChandiData" :key="parent.id" :id="`child_${parent.id}`">
            <a 
              v-for="child in parent.children" 
              :key="child.id"
              @click="toggleChildChandi(parent.id, child)"
              :class="['clr_3', 'mr_10', 'f_14', isChildSelected(parent.id, child.id) ? 'xuan_cur' : '']"
              :pid="parent.id"
              :myid="child.id"
            >
              {{ child.name }}
            </a>
          </span>
        </div>

        <!-- 范围滑块筛选 - 完全按照PHP原版 -->
        <div class="cls"></div>
        
        <!-- 长度筛选 -->
        <ul class="chandi_ul f_14 fl hang1" style="width:380px;">
          <li class="length">长　度</li>
          <li style="width:240px;">
            <el-slider
              v-model="rangeFilters.changdu"
              range
              :min="25"
              :max="32"
              :step="0.1"
              @change="updateChangduRange"
            />
            <div class="range-display">{{ rangeFilters.changdu[0] }} - {{ rangeFilters.changdu[1] }}</div>
          </li>
        </ul>

        <!-- 强力筛选 -->
        <ul class="chandi_ul f_14 fl hang1" style="width:380px;">
          <li class="length">强　力</li>
          <li style="width:240px;">
            <el-slider
              v-model="rangeFilters.qiangli"
              range
              :min="25"
              :max="32"
              :step="0.1"
              @change="updateQiangliRange"
            />
            <div class="range-display">{{ rangeFilters.qiangli[0] }} - {{ rangeFilters.qiangli[1] }}</div>
          </li>
        </ul>

        <!-- 马值筛选 -->
        <ul class="chandi_ul f_14 fl hang1" style="width:380px;">
          <li class="length">马　值</li>
          <li style="width:240px;">
            <el-slider
              v-model="rangeFilters.mazhi"
              range
              :min="2.5"
              :max="5.5"
              :step="0.1"
              @change="updateMazhiRange"
            />
            <div class="range-display">{{ rangeFilters.mazhi[0] }} - {{ rangeFilters.mazhi[1] }}</div>
          </li>
        </ul>

        <div class="cls"></div>

        <!-- 回潮筛选 -->
        <ul class="chandi_ul f_14 fl hang2" style="width:380px;">
          <li class="length">回　潮</li>
          <li style="width:240px;">
            <el-slider
              v-model="rangeFilters.huichao"
              range
              :min="0"
              :max="10"
              :step="0.1"
              @change="updateHuichaoRange"
            />
            <div class="range-display">{{ rangeFilters.huichao[0] }} - {{ rangeFilters.huichao[1] }}</div>
          </li>
        </ul>

        <!-- 整齐度筛选 -->
        <ul class="chandi_ul f_14 fl hang2" style="width:380px;">
          <li class="length">整齐度</li>
          <li style="width:240px;">
            <el-slider
              v-model="rangeFilters.zhengqidu"
              range
              :min="77"
              :max="90"
              :step="0.1"
              @change="updateZhengqiduRange"
            />
            <div class="range-display">{{ rangeFilters.zhengqidu[0] }} - {{ rangeFilters.zhengqidu[1] }}</div>
          </li>
        </ul>

        <!-- 含杂筛选 -->
        <ul class="chandi_ul f_14 fl hang2" style="width:380px;">
          <li class="length">含　杂</li>
          <li style="width:240px;">
            <el-slider
              v-model="rangeFilters.hanza"
              range
              :min="0"
              :max="6"
              :step="0.1"
              @change="updateHanzaRange"
            />
            <div class="range-display">{{ rangeFilters.hanza[0] }} - {{ rangeFilters.hanza[1] }}</div>
          </li>
        </ul>

        <div class="cls"></div>

        <!-- 交货仓库和轧花厂输入 - 完全按照PHP原版 -->
        <ul class="chandi_ul f_14 mt_10">
          <li>
            交货仓库：<input 
              class="border1 f_14 clr_9 pad_5" 
              placeholder="请输入仓库名称"
              v-model="searchForm.cangchumingcheng" 
              style="width:350px;" 
            />
            轧花厂：<input 
              class="border1 f_14 clr_9 pad_5" 
              placeholder="请输入轧花厂名称或编号"
              v-model="searchForm.jiagongchang"  
              style="width:350px;" 
            />
            <input 
              type="button" 
              @click="handleSearch" 
              value="✓ 确认筛选　" 
              class="f_14 clr_f bg_lan border0 pad_5" 
            />
            <a 
              @click="addToDingzhi" 
              style="font-weight: 700; color: #F1830E; padding-left: 10px; font-size: 15px; cursor: pointer;"
            >
              +添加到个性需求
            </a>
          </li>
        </ul>
      </div>

      <!-- 排序和导出区域 - 完全按照PHP原版 -->
      <div class="border1 bg_f mt_10 pad_10">
        <ul class="paixu_ul f_14 clearfix">
          <li class="clr_b f_wei">综合排序</li>
          <li style="margin-right: 15px;">
            <label class="mr_5 rad5" style="background: #e8e3df;padding:1px 5px 3px 5px;">
              <input type="checkbox" class="ver_mid" v-model="filters.hot" @change="handleSearch" />
              <span class="ver_mid">热门</span>
            </label>
          </li>
          <li style="border:0;">
            <input 
              v-model="exportCount" 
              placeholder="导出条数" 
              value="300" 
              class="ver_mid border1" 
              style="width:60px;" 
            />
            <a @click="exportExcel" style="cursor: pointer;">
              <img src="/images/excel.png" class="dis_ib ver_mid" /> 
              <span class="ver_mid" style="color:#217346;">导出报价</span>
            </a>
          </li>
          <div class="fr" style="border:1px solid #fff;">
            <li style="border:0;">
              <label class="mr_5">
                <input type="checkbox" class="ver_mid" v-model="filters.xunjia" @change="handleHeyueFilter('询价')" />
                <span class="ver_mid">询价</span>
              </label>
            </li>
            <li style="border:0;">
              <label class="mr_5">
                <input type="checkbox" class="ver_mid" v-model="filters.dabao" @change="handleHeyueFilter('打包')" />
                <span class="ver_mid">打包</span>
              </label>
            </li>
            <li style="border:0;">
              <label class="mr_5">
                <input type="checkbox" class="ver_mid" v-model="filters.jicha" @change="handleHeyueFilter('jicha')" />
                <span class="ver_mid">基差点价</span>
              </label>
            </li>
            <li style="border:0;">
              <label class="mr_5">
                <input type="checkbox" class="ver_mid" v-model="filters.yikoujia" @change="handleHeyueFilter('一口价')" />
                <span class="ver_mid">一口价</span>
              </label>
              <span class="ver_mid">共计{{ total }}批</span>
            </li>
          </div>
        </ul>
      </div>

      <!-- 产品列表 - 完全按照PHP原版 -->
      <div v-for="product in products" :key="product.id" class="border1 bg_gr pad_10 f_14 clr_3 clearfix mt_10 pos_rela" style="margin:10px 0;">
        <div class="fl" style="width:830px; font-size:16px;">
          <div style="font-size:16px;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;">
            <a @click="viewProduct(product.id)" class="clr_b f_wei" style="cursor: pointer;">{{ product.pihao_kunkao }}</a>
            <span class="ml_10">{{ product.leixing }}</span>
            <span class="ml_10">{{ product.baoshu }}件 &nbsp;</span>
            <span class="clr_3">{{ product.diqu_text }}</span>
            <span class="clr_9">加工厂 <span class="clr_3">{{ product.jiagongchang }}</span></span>
          </div>
          <div class="lh_30 mt_5" :title="product.yanseji_pinji" style="font-size:16px;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;">
            <span class="clr_9">颜色 <span class="clr_3">{{ product.yanseji_pinji }}</span></span>
          </div>
          <div class="lh_30" style="font-size:16px;">
            <span class="clr_9">长度 <span class="clr_3">{{ product.changdu }}</span></span>
            <span class="clr_9">强力 <span class="clr_3">{{ product.qiangli }}</span></span>
            <span class="clr_9">马值 <span class="clr_3">{{ product.mazhi }}</span></span>
            <span class="clr_9">含杂<span class="clr_3">{{ product.hanzalv }}</span></span>
            <span class="clr_9">回潮 <span class="clr_3">{{ product.huichaolv }}</span></span>
            <span class="clr_9">整齐度<span class="clr_3">{{ product.zhengqidu }}</span></span>
            <span class="clr_9">公重<span class="clr_3">{{ product.gongzhong }}吨</span></span>
            <span class="clr_9">轧工质量<span class="clr_3">{{ product.ygzl }}</span></span><br />
          </div>
          <div class="mt_5">
            <span class="clr_9">仓储名称 <span class="clr_3">{{ product.cangchumingcheng }}</span></span>
            <span class="clr_9" style="margin:0 15px;">
              <span class="clr_3">{{ product.diqu_text_cangku }}</span>
            </span>
            <span class="clr_9">更新时间： <span class="clr_3">{{ formatDate(product.add_time) }}</span></span>
          </div>
        </div>
        <div class="fr clearfix f_14 al_ct" style="width:250px;">
          <div class="clr_9">参考合约： <span>{{ product.dianjiaheyue }}</span></div>
          <div class="clr_9 pos_rela">基差： <span class="clr_3 f_20">{{ product.jicha }}</span></div>
          <a 
            v-if="product.is_favor === 1"
            @click="cancelFavor(product.id)" 
            class="f_14 zy_box clr_ora dis_ib cancel_favor mt_5" 
            style="background: #f1830e;color:#fff; cursor: pointer;"
          >
            <span class="ver_mid">已收藏</span>
          </a>
          <a 
            v-else
            @click="addFavor(product.id)" 
            class="f_14 zy_box clr_ora dis_ib add_favor mt_5"
            style="cursor: pointer;"
          >
            <span class="ver_mid">收藏</span>
          </a>
        </div>
      </div>

      <!-- 分页 - 完全按照PHP原版 -->
      <div class="pager mt_20" v-html="pageHtml"></div>
      <div class="pad_30"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getProducts, getFilterOptions, addToFavorites, removeFromFavorites, exportProducts } from '@/api/products'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据 - 完全按照PHP原版变量名
const chandi = ref<any[]>([])  // 主分类
const chandiDefault = ref<any[]>([])  // 产地选项
const xlsLeixing = ref<string[]>([])  // 类型选项
const xlsNiandu = ref<string[]>([])  // 年度选项
const products = ref<any[]>([])  // 产品列表
const total = ref(0)  // 总数
const pageHtml = ref('')  // 分页HTML

// 当前选择的主分类
const currentDef = ref(1)

// 选中的筛选条件
const selectedParentChandi = ref<number[]>([])
const selectedChildChandi = ref<{[key: number]: number[]}>({})
const selectedLeixing = ref<string[]>([])
const selectedNiandu = ref<string[]>([])

// 范围筛选
const rangeFilters = reactive({
  changdu: [25, 32],
  qiangli: [25, 32],
  mazhi: [2.5, 5.5],
  huichao: [0, 10],
  zhengqidu: [77, 90],
  hanza: [0, 6]
})

// 搜索表单
const searchForm = reactive({
  cangchumingcheng: '',
  jiagongchang: '',
  sk: ''
})

// 其他筛选
const filters = reactive({
  hot: false,
  xunjia: false,
  dabao: false,
  jicha: false,
  yikoujia: false
})

const exportCount = ref('300')

// 计算属性
const selectedParentChandiData = computed(() => {
  return chandiDefault.value.filter(item => selectedParentChandi.value.includes(item.id))
})

// 方法
const selectMainCategory = (id: number) => {
  currentDef.value = id
  handleSearch()
}

const toggleParentChandi = async (item: any) => {
  const index = selectedParentChandi.value.indexOf(item.id)
  if (index > -1) {
    selectedParentChandi.value.splice(index, 1)
    // 清除对应的子产地选择
    delete selectedChildChandi.value[item.id]
  } else {
    selectedParentChandi.value.push(item.id)
    // 获取子产地数据
    try {
      // 这里应该调用API获取子产地数据
      // const response = await getChildChandi(item.id)
      // item.children = response.data
    } catch (error) {
      ElMessage.error('获取子产地数据失败')
    }
  }
}

const toggleChildChandi = (parentId: number, child: any) => {
  if (!selectedChildChandi.value[parentId]) {
    selectedChildChandi.value[parentId] = []
  }

  const index = selectedChildChandi.value[parentId].indexOf(child.id)
  if (index > -1) {
    selectedChildChandi.value[parentId].splice(index, 1)
  } else {
    selectedChildChandi.value[parentId].push(child.id)
  }
}

const isChildSelected = (parentId: number, childId: number) => {
  return selectedChildChandi.value[parentId]?.includes(childId) || false
}

const toggleLeixing = (item: string) => {
  const index = selectedLeixing.value.indexOf(item)
  if (index > -1) {
    selectedLeixing.value.splice(index, 1)
  } else {
    selectedLeixing.value.push(item)
  }
}

const toggleNiandu = (item: string) => {
  const index = selectedNiandu.value.indexOf(item)
  if (index > -1) {
    selectedNiandu.value.splice(index, 1)
  } else {
    selectedNiandu.value.push(item)
  }
}

// 范围更新方法
const updateChangduRange = (value: number[]) => {
  rangeFilters.changdu = value
}

const updateQiangliRange = (value: number[]) => {
  rangeFilters.qiangli = value
}

const updateMazhiRange = (value: number[]) => {
  rangeFilters.mazhi = value
}

const updateHuichaoRange = (value: number[]) => {
  rangeFilters.huichao = value
}

const updateZhengqiduRange = (value: number[]) => {
  rangeFilters.zhengqidu = value
}

const updateHanzaRange = (value: number[]) => {
  rangeFilters.hanza = value
}

const handleHeyueFilter = (type: string) => {
  // 重置其他合约筛选
  Object.keys(filters).forEach(key => {
    if (key !== 'hot' && key !== type.toLowerCase()) {
      filters[key as keyof typeof filters] = false
    }
  })
  handleSearch()
}

const handleSearch = async () => {
  try {
    const params = {
      def: currentDef.value,
      cd: selectedParentChandi.value.join(','),
      cd2: JSON.stringify(selectedChildChandi.value),
      leixing: selectedLeixing.value.join('|'),
      pihao_kunkao: selectedNiandu.value.join('|'),
      changdu_from: rangeFilters.changdu[0],
      changdu_to: rangeFilters.changdu[1],
      qiangli_from: rangeFilters.qiangli[0],
      qiangli_to: rangeFilters.qiangli[1],
      mazhi_from: rangeFilters.mazhi[0],
      mazhi_to: rangeFilters.mazhi[1],
      huichaolv_from: rangeFilters.huichao[0],
      huichaolv_to: rangeFilters.huichao[1],
      zhengqidu_from: rangeFilters.zhengqidu[0],
      zhengqidu_to: rangeFilters.zhengqidu[1],
      hanzalv_from: rangeFilters.hanza[0],
      hanzalv_to: rangeFilters.hanza[1],
      cangchumingcheng: searchForm.cangchumingcheng,
      jiagongchang: searchForm.jiagongchang,
      sk: searchForm.sk,
      hot: filters.hot ? 1 : 0,
      heyue: getSelectedHeyue()
    }

    const response = await getProducts(params)
    products.value = response.data?.lists || []
    total.value = response.data?.count || 0
    pageHtml.value = response.data?.page || ''
  } catch (error) {
    ElMessage.error('获取产品数据失败')
  }
}

const getSelectedHeyue = () => {
  if (filters.xunjia) return '询价'
  if (filters.dabao) return '打包'
  if (filters.jicha) return 'jicha'
  if (filters.yikoujia) return '一口价'
  return ''
}

const viewProduct = (id: number) => {
  router.push(`/public/products/${id}`)
}

const addFavor = async (id: number) => {
  try {
    await addToFavorites(id)
    ElMessage.success('收藏成功')
    handleSearch() // 重新获取数据
  } catch (error) {
    ElMessage.error('收藏失败')
  }
}

const cancelFavor = async (id: number) => {
  try {
    await ElMessageBox.confirm('确认取消收藏？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await removeFromFavorites(id)
    ElMessage.success('取消收藏成功')
    handleSearch() // 重新获取数据
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('取消收藏失败')
    }
  }
}

const addToDingzhi = () => {
  // 添加到个性需求的逻辑
  ElMessage.info('添加到个性需求功能开发中')
}

const exportExcel = async () => {
  try {
    if (!userStore.isLoggedIn) {
      ElMessage.error('请先登录')
      return
    }

    const count = parseInt(exportCount.value)
    if (!count || count <= 0) {
      ElMessage.error('请输入有效的导出条数')
      return
    }

    // 根据用户类型限制导出条数
    const maxCount = userStore.userInfo.member_type === 1 ? 45000 : 300
    if (count > maxCount) {
      ElMessage.error(`最大不超过${maxCount}条`)
      return
    }

    const params = {
      // ... 所有筛选参数
      export_count: count,
      act: 'excel'
    }

    const response = await exportProducts(params)
    if (response.data?.url) {
      window.location.href = response.data.url
    }
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const formatDate = (timestamp: number) => {
  const date = new Date(timestamp * 1000)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取筛选选项数据
const fetchFilterOptions = async () => {
  try {
    const response = await getFilterOptions()
    chandi.value = response.data?.chandi || []
    chandiDefault.value = response.data?.chandi_default || []
    xlsLeixing.value = response.data?.xls_leixing || []
    xlsNiandu.value = response.data?.xls_niandu || []
  } catch (error) {
    ElMessage.error('获取筛选选项失败')
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchFilterOptions()
  handleSearch()
})
</script>

<style scoped>
/* 完全按照PHP原版样式 */

/* 主分类导航样式 */
.mian_ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mian_ul li {
  display: inline-block;
  margin-right: 20px;
}

.mian_ul li a {
  display: block;
  padding: 10px 20px;
  text-decoration: none;
  border-radius: 5px;
  transition: all 0.3s;
}

.mian_norm {
  background: #f5f5f5;
  color: #333;
}

.mian_norm:hover {
  background: #e0e0e0;
}

.mian_cur {
  background: #28afe5;
  color: #fff !important;
}

/* 筛选区域样式 */
.chandi_ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.chandi_ul li {
  display: inline-block;
  margin-right: 10px;
  margin-bottom: 5px;
}

.chandi_ul li a {
  display: inline-block;
  padding: 5px 10px;
  text-decoration: none;
  border: 1px solid #ddd;
  border-radius: 3px;
  transition: all 0.3s;
  cursor: pointer;
}

.chandi_ul li a:hover {
  background: #f0f0f0;
}

.xuan_cur {
  background: #28afe5 !important;
  color: #fff !important;
  border-color: #28afe5 !important;
}

/* 范围滑块样式 */
.hang1, .hang2 {
  margin-bottom: 15px;
}

.length {
  width: 80px;
  display: inline-block;
  text-align: right;
  margin-right: 10px;
}

.range-display {
  text-align: center;
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

/* 排序区域样式 */
.paixu_ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.paixu_ul li {
  display: inline-block;
  margin-right: 15px;
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 3px;
}

.paixu_ul li:first-child {
  border: none;
  background: none;
}

/* 产品列表样式 */
.bg_gr {
  background: #f9f9f9;
}

.product-item:hover {
  background: #f5f5f5;
}

/* 收藏按钮样式 */
.zy_box {
  display: inline-block;
  padding: 5px 15px;
  border: 1px solid #f1830e;
  border-radius: 3px;
  text-decoration: none;
  transition: all 0.3s;
}

.zy_box:hover {
  background: #f1830e;
  color: #fff !important;
}

.cancel_favor {
  background: #f1830e;
  color: #fff;
}

.cancel_favor:hover {
  background: #e55a00;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chandi_ul {
    width: 100% !important;
    margin-left: 0 !important;
  }

  .hang1, .hang2 {
    width: 100% !important;
  }

  .fl {
    float: none !important;
    width: 100% !important;
  }

  .fr {
    float: none !important;
    width: 100% !important;
    margin-top: 10px;
  }
}
</style>
