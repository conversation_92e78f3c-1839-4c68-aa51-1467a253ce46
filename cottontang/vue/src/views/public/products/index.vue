<template>
  <div class="products-page">
    <div class="container">
      <div class="page-header">
        <h1>产品展示</h1>
        <p>优质棉花产品，满足您的各种需求</p>
      </div>

      <!-- 搜索和筛选 -->
      <div class="search-section">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索产品..."
              @keyup.enter="handleSearch"
            >
              <template #append>
                <el-button @click="handleSearch">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select v-model="searchForm.danType" placeholder="选择类型" @change="handleSearch">
              <el-option label="全部" value="" />
              <el-option label="仓单" value="仓单" />
              <el-option label="在售" value="在售" />
              <el-option label="挂单" value="挂单" />
            </el-select>
          </el-col>
        </el-row>
      </div>

      <!-- 产品列表 -->
      <div class="products-grid" v-loading="loading">
        <div class="product-card" v-for="product in products" :key="product.id">
          <div class="product-info">
            <h3>{{ product.leixing }}</h3>
            <div class="product-details">
              <p><strong>批号/捆号:</strong> {{ product.pihaoKunkao }}</p>
              <p><strong>颜色级/品级:</strong> {{ product.yansejiPinji }}</p>
              <p><strong>长度:</strong> {{ product.changdu }}</p>
              <p><strong>强力:</strong> {{ product.qiangli }}</p>
              <p><strong>加工厂:</strong> {{ product.jiagongchang }}</p>
              <p><strong>仓储名称:</strong> {{ product.cangchumingcheng }}</p>
              <p><strong>状态:</strong> 
                <el-tag :type="getStatusType(product.danType)">{{ product.danType }}</el-tag>
              </p>
            </div>
            <div class="product-actions">
              <el-button type="primary" @click="viewDetail(product.id)">查看详情</el-button>
              <el-button @click="addToFavorite(product.id)">收藏</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { getHomepageList, addFavorite } from '@/api/homepage'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const products = ref([])

const searchForm = reactive({
  keyword: '',
  danType: ''
})

const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0
})

// 获取产品列表
const fetchProducts = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword,
      danType: searchForm.danType
    }
    
    const response = await getHomepageList(params)
    products.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取产品列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.pageNum = 1
  fetchProducts()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchProducts()
}

const handleCurrentChange = (page: number) => {
  pagination.pageNum = page
  fetchProducts()
}

// 查看详情
const viewDetail = (id: number) => {
  router.push(`/public/products/${id}`)
}

// 添加收藏
const addToFavorite = async (id: number) => {
  try {
    await addFavorite(id, 1) // 1表示产品类型
    ElMessage.success('收藏成功')
  } catch (error) {
    ElMessage.error('收藏失败，请先登录')
  }
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '在售': return 'success'
    case '仓单': return 'info'
    case '挂单': return 'warning'
    case '已售': return 'danger'
    default: return ''
  }
}

onMounted(() => {
  fetchProducts()
})
</script>

<style scoped>
.products-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 10px;
}

.page-header p {
  font-size: 1.1rem;
  color: #666;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.product-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.product-card:hover {
  transform: translateY(-2px);
}

.product-info h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.product-details p {
  margin: 8px 0;
  color: #666;
}

.product-actions {
  margin-top: 15px;
  text-align: right;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}
</style>
