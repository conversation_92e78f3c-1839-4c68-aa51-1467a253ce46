<template>
  <div class="home-page">
    <!-- 每日精选 -->
    <div class="daily-selection">
      <div class="container">
        <div class="section-title">每日精选</div>
        <div class="table-container">
          <el-table :data="dailyProducts" stripe style="width: 100%" class="cotton-table">
            <el-table-column prop="pihaoKunkao" label="批号" width="120" align="center">
              <template #default="scope">
                <el-link type="primary" @click="viewProduct(scope.row.id)">
                  {{ scope.row.pihaoKunkao }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column prop="leixing" label="类型" width="100" align="center" />
            <el-table-column prop="yansejiPinji" label="颜色级" width="120" align="center">
              <template #default="scope">
                <el-tooltip :content="scope.row.yansejiPinji" placement="top">
                  <span>{{ truncateText(scope.row.yansejiPinji, 6) }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="mazhi" label="马值" width="80" align="center" />
            <el-table-column prop="changdu" label="长度" width="80" align="center" />
            <el-table-column prop="qiangli" label="强力" width="80" align="center" />
            <el-table-column prop="hanzalv" label="含杂" width="80" align="center" />
            <el-table-column prop="huichaolv" label="回潮" width="80" align="center" />
            <el-table-column prop="gongzhong" label="公重" width="80" align="center" />
            <el-table-column prop="zhengqidu" label="整齐度" width="100" align="center" />
            <el-table-column prop="jiagongchang" label="加工厂" width="120" align="center">
              <template #default="scope">
                <el-tooltip :content="scope.row.jiagongchang" placement="top">
                  <span>{{ truncateText(scope.row.jiagongchang, 6) }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="cangchumingcheng" label="仓库" width="120" align="center">
              <template #default="scope">
                <el-tooltip :content="scope.row.cangchumingcheng" placement="top">
                  <span>{{ truncateText(scope.row.cangchumingcheng, 6) }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="jicha" label="基差" width="80" align="center" />
            <el-table-column prop="dianjiaheyue" label="点价合约" width="120" align="center" />
          </el-table>
        </div>
      </div>
    </div>

    <!-- 轮播图 -->
    <div class="banner-section">
      <el-carousel height="450px" indicator-position="outside" arrow="hover">
        <el-carousel-item v-for="(banner, index) in banners" :key="index">
          <div class="banner-item" :style="{ backgroundImage: `url(${banner.thumbs})` }">
            <a v-if="banner.url" :href="banner.url" target="_blank" class="banner-link"></a>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>

    <!-- 个性需求 -->
    <div class="custom-requirements" v-if="isLoggedIn">
      <div class="container">
        <div class="section-title">个性需求</div>
        <div v-for="(requirement, index) in customRequirements" :key="index" class="requirement-item">
          <div class="requirement-content">
            <div class="requirement-header">
              <el-link type="primary" class="requirement-no">{{ requirement.dingzhiNo }}</el-link>
              <span class="requirement-info">类型：<strong>{{ requirement.leixing }}</strong></span>
              <span class="requirement-info">年度：<strong>{{ requirement.pihaoKunkao }}</strong></span>
            </div>
            <div class="requirement-details">
              <span class="detail-item">长度：<strong>{{ requirement.changdu }}</strong></span>
              <span class="detail-item">马值：<strong>{{ requirement.mazhi }}</strong></span>
              <span class="detail-item">强力：<strong>{{ requirement.qiangli }}</strong></span>
              <span class="detail-item">含杂：<strong>{{ requirement.hanzalv }}</strong></span>
              <span class="detail-item">回潮：<strong>{{ requirement.huichaolv }}</strong></span>
              <span class="detail-item">公重：<strong>{{ requirement.gongzhong }}</strong></span>
              <span class="detail-item">整齐度：<strong>{{ requirement.zhengqidu }}</strong></span>
              <span class="detail-item">轧花厂：<strong>{{ requirement.jiagongchang }}</strong></span>
              <span class="detail-item">交货仓库：<strong>{{ requirement.cangchumingcheng }}</strong></span>
            </div>
          </div>
          <div class="requirement-actions">
            <el-button type="danger" size="small" @click="deleteRequirement(requirement.id)">删除</el-button>
            <el-button type="primary" size="small" @click="viewResources(requirement.url)">
              <el-icon><Search /></el-icon>
              查看资源
            </el-button>
          </div>
        </div>
        <div v-if="customRequirements.length === 0" class="no-requirements">
          <p>暂无个性需求</p>
          <el-button type="primary" @click="createRequirement">创建需求</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { getHomepageData, getBanners, getCustomRequirements, deleteCustomRequirement } from '@/api/public'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const dailyProducts = ref([])
const banners = ref([])
const customRequirements = ref([])

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 文本截取函数
const truncateText = (text: string, length: number) => {
  if (!text) return ''
  return text.length > length ? text.substring(0, length) + '...' : text
}

// 获取首页数据
const fetchHomepageData = async () => {
  try {
    const response = await getHomepageData()
    dailyProducts.value = response.data || []
  } catch (error) {
    ElMessage.error('获取每日精选数据失败')
  }
}

// 获取轮播图数据
const fetchBanners = async () => {
  try {
    const response = await getBanners()
    banners.value = response.data || []
  } catch (error) {
    ElMessage.error('获取轮播图数据失败')
  }
}

// 获取个性需求数据
const fetchCustomRequirements = async () => {
  if (!isLoggedIn.value) return

  try {
    const response = await getCustomRequirements()
    customRequirements.value = response.data || []
  } catch (error) {
    ElMessage.error('获取个性需求数据失败')
  }
}

// 查看产品详情
const viewProduct = (id: number) => {
  router.push(`/public/products/${id}`)
}

// 删除个性需求
const deleteRequirement = async (id: number) => {
  try {
    await ElMessageBox.confirm('确认删除这个需求吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteCustomRequirement(id)
    ElMessage.success('删除成功')
    fetchCustomRequirements() // 重新获取数据
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 查看资源
const viewResources = (url: string) => {
  if (url) {
    router.push(url)
  }
}

// 创建需求
const createRequirement = () => {
  router.push('/user/custom-orders')
}

// 页面加载时获取数据
onMounted(() => {
  fetchHomepageData()
  fetchBanners()
  fetchCustomRequirements()
})
</script>

<style scoped>
.home-page {
  background-color: #f2f2f2;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 每日精选样式 */
.daily-selection {
  background-color: #f2f2f2;
  padding: 20px 0;
}

.section-title {
  background-color: #28afe5;
  color: #fff;
  padding: 10px 20px;
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: normal;
}

.table-container {
  background-color: #fff;
  border: 1px solid #ddd;
  overflow-x: auto;
}

.cotton-table {
  font-size: 16px;
}

.cotton-table :deep(.el-table__header) {
  background-color: #28afe5;
}

.cotton-table :deep(.el-table__header th) {
  background-color: #28afe5 !important;
  color: #fff;
  text-align: center;
  font-weight: normal;
}

.cotton-table :deep(.el-table__body tr:hover > td) {
  background-color: #f5f7fa;
}

/* 轮播图样式 */
.banner-section {
  margin: 20px 0;
}

.banner-item {
  height: 450px;
  background-size: auto 100%;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}

.banner-link {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: block;
}

/* 个性需求样式 */
.custom-requirements {
  background-color: #f2f2f2;
  padding: 20px 0;
}

.requirement-item {
  background-color: #fff;
  border: 1px solid #ddd;
  padding: 20px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.requirement-content {
  flex: 1;
  margin-right: 20px;
}

.requirement-header {
  margin-bottom: 10px;
}

.requirement-no {
  font-weight: bold;
  margin-right: 10px;
}

.requirement-info {
  color: #999;
  margin-right: 10px;
}

.requirement-info strong {
  color: #333;
}

.requirement-details {
  line-height: 30px;
}

.detail-item {
  color: #999;
  margin-right: 10px;
  display: inline-block;
}

.detail-item strong {
  color: #333;
}

.requirement-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-width: 160px;
  text-align: right;
}

.no-requirements {
  text-align: center;
  padding: 40px 0;
  color: #999;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .requirement-item {
    flex-direction: column;
  }

  .requirement-content {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .requirement-actions {
    flex-direction: row;
    justify-content: center;
    min-width: auto;
  }

  .table-container {
    font-size: 14px;
  }
}
</style> 