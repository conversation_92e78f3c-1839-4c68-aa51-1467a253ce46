<template>
  <div class="home-page">
    <!-- 轮播图 -->
    <div class="banner-section">
      <el-carousel height="500px" indicator-position="outside">
        <el-carousel-item v-for="(item, index) in banners" :key="index">
          <div class="banner-item" :style="{ backgroundImage: `url(${item.imageUrl})` }">
            <div class="container banner-content">
              <h2>{{ item.title }}</h2>
              <p>{{ item.description }}</p>
              <el-button type="primary" size="large" v-if="item.buttonText" @click="navigateTo(item.link)">
                {{ item.buttonText }}
              </el-button>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
    
    <!-- 产品类别 -->
    <div class="section category-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">产品类别</h2>
          <p class="section-subtitle">提供多种优质棉花产品，满足不同需求</p>
        </div>
        <div class="category-list">
          <div v-for="(category, index) in categories" :key="index" class="category-item">
            <div class="category-image">
              <img :src="category.imageUrl" :alt="category.name">
            </div>
            <h3>{{ category.name }}</h3>
            <p>{{ category.description }}</p>
            <router-link :to="`/products?category=${category.id}`" class="more-link">查看更多</router-link>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 热门产品 -->
    <div class="section products-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">热门产品</h2>
          <p class="section-subtitle">精选优质棉花产品，为您提供卓越体验</p>
        </div>
        <el-row :gutter="20">
          <el-col :span="6" v-for="(product, index) in hotProducts" :key="index">
            <div class="product-card">
              <div class="product-image">
                <img :src="product.imageUrl" :alt="product.name">
              </div>
              <div class="product-info">
                <h3 class="product-name">{{ product.name }}</h3>
                <p class="product-category">{{ product.categoryName }}</p>
                <p class="product-price">¥{{ product.price.toFixed(2) }}</p>
                <div class="product-actions">
                  <el-button type="primary" size="small" @click="viewProduct(product.id)">查看详情</el-button>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <div class="view-more">
          <router-link to="/products" class="view-more-link">
            查看全部产品 <i class="el-icon-right"></i>
          </router-link>
        </div>
      </div>
    </div>
    
    <!-- 公司简介 -->
    <div class="section about-section">
      <div class="container">
        <el-row :gutter="40">
          <el-col :span="12">
            <div class="about-image">
              <img src="https://shadow.elemecdn.com/app/element/hamburger.9cf7b091-55e9-11e9-a976-7f4d0b07eef6.png" alt="公司简介">
            </div>
          </el-col>
          <el-col :span="12">
            <div class="about-content">
              <h2 class="section-title">关于我们</h2>
              <p>棉花糖是一家专注于棉花全产业链的领先企业，致力于为客户提供高品质、多样化的棉花产品与服务。</p>
              <p>我们拥有先进的生产设备和专业的技术团队，严格把控每一个环节的质量，确保产品达到最高标准。</p>
              <p>自成立以来，我们秉持"质量为本，客户至上"的原则，已与众多国内外企业建立了长期稳定的合作关系。</p>
              <router-link to="/about" class="more-link">了解更多 <i class="el-icon-right"></i></router-link>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    
    <!-- 新闻动态 -->
    <div class="section news-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">新闻动态</h2>
          <p class="section-subtitle">了解最新行业动态和公司新闻</p>
        </div>
        <el-row :gutter="30">
          <el-col :span="8" v-for="(news, index) in newsList" :key="index">
            <div class="news-card">
              <div class="news-image">
                <img :src="news.imageUrl" :alt="news.title">
                <span class="news-date">{{ news.date }}</span>
              </div>
              <div class="news-content">
                <h3 class="news-title">{{ news.title }}</h3>
                <p class="news-desc">{{ news.summary }}</p>
                <a href="javascript:;" @click="viewNews(news.id)" class="more-link">阅读更多</a>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 轮播图数据
const banners = ref([
  {
    title: '新疆优质棉花',
    description: '精选新疆优质棉花，品质保证',
    imageUrl: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
    buttonText: '了解更多',
    link: '/about'
  },
  {
    title: '全产业链服务',
    description: '从种植到加工，全产业链质量控制',
    imageUrl: 'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
    buttonText: '查看产品',
    link: '/products'
  },
  {
    title: '专业团队支持',
    description: '专业技术团队，为您提供全方位解决方案',
    imageUrl: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
    buttonText: '联系我们',
    link: '/contact'
  }
])

// 产品类别数据
const categories = ref([
  {
    id: 1,
    name: '长绒棉',
    description: '优质长绒棉，纤维长度超过32mm',
    imageUrl: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    id: 2,
    name: '短绒棉',
    description: '标准短绒棉，性价比高',
    imageUrl: 'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg'
  },
  {
    id: 3,
    name: '彩色棉',
    description: '天然彩色棉，无需染色',
    imageUrl: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg'
  },
  {
    id: 4,
    name: '有机棉',
    description: '无污染有机棉，环保健康',
    imageUrl: 'https://shadow.elemecdn.com/app/element/hamburger.9cf7b091-55e9-11e9-a976-7f4d0b07eef6.png'
  }
])

// 热门产品数据
const hotProducts = ref([
  {
    id: 1,
    name: '新疆长绒棉 5kg装',
    categoryName: '长绒棉',
    price: 199.00,
    imageUrl: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    id: 2,
    name: '新疆短绒棉 10kg装',
    categoryName: '短绒棉',
    price: 299.00,
    imageUrl: 'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg'
  },
  {
    id: 3,
    name: '有机棉花 3kg装',
    categoryName: '有机棉',
    price: 159.00,
    imageUrl: 'https://shadow.elemecdn.com/app/element/hamburger.9cf7b091-55e9-11e9-a976-7f4d0b07eef6.png'
  },
  {
    id: 4,
    name: '彩色棉 2kg装',
    categoryName: '彩色棉',
    price: 179.00,
    imageUrl: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg'
  }
])

// 新闻动态数据
const newsList = ref([
  {
    id: 1,
    title: '2023年棉花产业发展趋势分析',
    summary: '本文分析了2023年全球棉花产业的发展趋势，包括市场需求、价格走势和技术创新等方面。',
    date: '2023-07-20',
    imageUrl: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
  },
  {
    id: 2,
    title: '我公司荣获"质量信誉AAA级企业"称号',
    summary: '近日，我公司凭借卓越的产品质量和良好的企业信誉，被评为"质量信誉AAA级企业"。',
    date: '2023-07-15',
    imageUrl: 'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg'
  },
  {
    id: 3,
    title: '有机棉花种植技术研讨会成功举办',
    summary: '为推动有机棉花种植技术的发展，我公司与多家科研机构联合举办了有机棉花种植技术研讨会。',
    date: '2023-07-10',
    imageUrl: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg'
  }
])

// 导航到指定链接
const navigateTo = (link: string) => {
  router.push(link)
}

// 查看产品详情
const viewProduct = (id: number) => {
  router.push(`/products/${id}`)
}

// 查看新闻详情
const viewNews = (id: number) => {
  router.push(`/news/${id}`)
}
</script>

<style scoped>
/* 通用样式 */
.section {
  padding: 60px 0;
}

.section-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}

.section-subtitle {
  font-size: 16px;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

.more-link {
  display: inline-block;
  color: #409EFF;
  text-decoration: none;
  font-weight: 500;
  margin-top: 10px;
}

.more-link:hover {
  text-decoration: underline;
}

/* 轮播图样式 */
.banner-item {
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
}

.banner-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.banner-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: #fff;
}

.banner-content h2 {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 20px;
}

.banner-content p {
  font-size: 18px;
  margin-bottom: 30px;
  max-width: 600px;
}

/* 产品类别样式 */
.category-section {
  background-color: #f5f7fa;
}

.category-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.category-item {
  width: 23%;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
  text-align: center;
  padding-bottom: 20px;
}

.category-item:hover {
  transform: translateY(-5px);
}

.category-image {
  height: 200px;
  overflow: hidden;
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-item h3 {
  margin: 15px 0 10px;
  font-size: 18px;
  color: #333;
}

.category-item p {
  font-size: 14px;
  color: #666;
  padding: 0 15px;
  margin-bottom: 15px;
}

/* 产品卡片样式 */
.product-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  transition: transform 0.3s;
}

.product-card:hover {
  transform: translateY(-5px);
}

.product-image {
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 15px;
}

.product-name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.product-category {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.product-price {
  font-size: 18px;
  font-weight: bold;
  color: #f56c6c;
  margin-bottom: 15px;
}

.product-actions {
  display: flex;
  justify-content: center;
}

.view-more {
  text-align: center;
  margin-top: 30px;
}

.view-more-link {
  display: inline-block;
  font-size: 16px;
  color: #409EFF;
  text-decoration: none;
}

/* 关于我们样式 */
.about-section {
  background-color: #fff;
}

.about-image {
  height: 350px;
  overflow: hidden;
  border-radius: 8px;
}

.about-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.about-content {
  padding: 20px 0;
}

.about-content .section-title {
  text-align: left;
  margin-bottom: 20px;
}

.about-content p {
  font-size: 16px;
  color: #666;
  margin-bottom: 15px;
  line-height: 1.8;
}

/* 新闻卡片样式 */
.news-section {
  background-color: #f5f7fa;
}

.news-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  height: 100%;
}

.news-image {
  height: 200px;
  overflow: hidden;
  position: relative;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-date {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: #409EFF;
  color: #fff;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
}

.news-content {
  padding: 20px;
}

.news-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.news-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .category-item {
    width: 48%;
    margin-bottom: 20px;
  }
}

@media (max-width: 576px) {
  .category-item {
    width: 100%;
  }
}
</style> 