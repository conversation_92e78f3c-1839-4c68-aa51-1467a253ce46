<template>
  <div class="home-page bg_f2">
    <!-- 每日精选 -->
    <div class="m_auto">
      <div class="bar">每日精选</div>
      <div style="overflow-x: auto;">
        <table class="chaoshi_tbl f_16 clr_3 w_100p mt_10 bg_f">
          <tr style="background: #28afe5;text-align: center;" class="clr_f">
            <th>批号</th>
            <th>类型</th>
            <th>颜色级</th>
            <th>马值</th>
            <th>长度</th>
            <th>强力</th>
            <th>含杂</th>
            <th>回潮</th>
            <th>公重</th>
            <th>整齐度</th>
            <th>加工厂</th>
            <th>仓库</th>
            <th>基差</th>
            <th>点价合约</th>
          </tr>
          <tr v-for="item in chaoshi" :key="item.id" style="text-align:center;">
            <td>
              <div class="dis_ib ver_mid">
                <a :href="`/public/products/${item.id}`" class="clr_b f_wei">{{ item.pihao_kunkao }}</a>
              </div>
            </td>
            <td>{{ item.leixing }}</td>
            <td>
              <div :title="item.yanseji_pinji">{{ truncateText(item.yanseji_pinji, 6) }}</div>
            </td>
            <td>{{ item.mazhi }}</td>
            <td>{{ item.changdu }}</td>
            <td>{{ item.qiangli }}</td>
            <td>{{ item.hanzalv }}</td>
            <td>{{ item.huichaolv }}</td>
            <td>{{ item.gongzhong }}</td>
            <td>{{ item.zhengqidu }}</td>
            <td>
              <div :title="item.jiagongchang">{{ truncateText(item.jiagongchang, 6) }}</div>
            </td>
            <td>
              <div :title="item.cangchumingcheng">{{ truncateText(item.cangchumingcheng, 6) }}</div>
            </td>
            <td>{{ item.jicha }}</td>
            <td>{{ item.dianjiaheyue }}</td>
          </tr>
        </table>
      </div>
    </div>

    <!-- 轮播图 -->
    <div class="m_auto mt_20" v-if="lunbo.length > 0">
      <div class="lunbo_box pos_rela">
        <div class="lunbo_list" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
          <div
            v-for="(item, index) in lunbo"
            :key="index"
            class="lunbo_item"
            :style="{ backgroundImage: `url(${getImageUrl(item.thumbs)})` }"
          >
            <a v-if="item.url" :href="item.url" target="_blank" class="lunbo_link"></a>
          </div>
        </div>
        <div class="lunbo_dots">
          <span
            v-for="(item, index) in lunbo"
            :key="index"
            :class="['dot', { active: index === currentSlide }]"
            @click="goToSlide(index)"
          ></span>
        </div>
        <div class="lunbo_prev" @click="prevSlide">‹</div>
        <div class="lunbo_next" @click="nextSlide">›</div>
      </div>
    </div>

    <!-- 个性需求 -->
    <div class="m_auto mt_20" v-if="isLoggedIn">
      <div class="bar">个性需求</div>
      <div v-for="item in dingzhi" :key="item.id" class="dingzhi_item bg_f mt_10 pad_20 pos_rela">
        <div class="fl w_80p">
          <div class="f_16 clr_3 mb_10">
            <a :href="item.url" class="clr_b f_wei mr_20">{{ item.dingzhi_no }}</a>
            <span class="clr_9">类型：<strong class="clr_3">{{ item.leixing }}</strong></span>
            <span class="clr_9 ml_20">年度：<strong class="clr_3">{{ item.pihao_kunkao }}</strong></span>
          </div>
          <div class="f_14 clr_9 lh_28">
            <span class="mr_20">长度：<strong class="clr_3">{{ item.changdu }}</strong></span>
            <span class="mr_20">马值：<strong class="clr_3">{{ item.mazhi }}</strong></span>
            <span class="mr_20">强力：<strong class="clr_3">{{ item.qiangli }}</strong></span>
            <span class="mr_20">含杂：<strong class="clr_3">{{ item.hanzalv }}</strong></span>
            <span class="mr_20">回潮：<strong class="clr_3">{{ item.huichaolv }}</strong></span>
            <span class="mr_20">公重：<strong class="clr_3">{{ item.gongzhong }}</strong></span>
            <span class="mr_20">整齐度：<strong class="clr_3">{{ item.zhengqidu }}</strong></span>
            <span class="mr_20">轧花厂：<strong class="clr_3">{{ item.jiagongchang }}</strong></span>
            <span class="mr_20">交货仓库：<strong class="clr_3">{{ item.cangchumingcheng }}</strong></span>
          </div>
        </div>
        <div class="fr w_20p al_rt">
          <a @click="deleteDingzhi(item.id)" class="clr_r f_14 pointer mr_10">删除</a>
          <a :href="item.url" class="bg_b clr_f pad_5 f_14">查看资源</a>
        </div>
        <div class="clearfix"></div>
      </div>
      <div v-if="dingzhi.length === 0" class="bg_f pad_30 al_ct mt_10">
        <div class="clr_9 f_16 mb_20">暂无个性需求</div>
        <a href="/user/custom-orders" class="bg_b clr_f pad_10 f_16">创建需求</a>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

import { getHomepageData, getBanners, getCustomRequirements, deleteCustomRequirement } from '@/api/public'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据 - 完全按照PHP原版
const chaoshi = ref<any[]>([])  // 每日精选数据
const lunbo = ref<any[]>([])    // 轮播图数据
const dingzhi = ref<any[]>([])  // 个性需求数据

// 轮播图相关
const currentSlide = ref(0)
let slideTimer: NodeJS.Timeout | null = null

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 文本截取函数
const truncateText = (text: string, length: number) => {
  if (!text) return ''
  return text.length > length ? text.substring(0, length) + '...' : text
}

// 轮播图相关方法
const getImageUrl = (thumbs: string) => {
  if (!thumbs) return '/images/default-banner.jpg'
  return thumbs.startsWith('http') ? thumbs : `/api/files/${thumbs}`
}

const goToSlide = (index: number) => {
  currentSlide.value = index
}

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % lunbo.value.length
}

const prevSlide = () => {
  currentSlide.value = currentSlide.value === 0 ? lunbo.value.length - 1 : currentSlide.value - 1
}

const startAutoSlide = () => {
  if (lunbo.value.length > 1) {
    slideTimer = setInterval(() => {
      nextSlide()
    }, 5000)
  }
}

const stopAutoSlide = () => {
  if (slideTimer) {
    clearInterval(slideTimer)
    slideTimer = null
  }
}

// 获取首页数据 - 完全按照PHP原版
const fetchHomepageData = async () => {
  try {
    const response = await getHomepageData()
    chaoshi.value = response.data?.chaoshi || []
  } catch (error) {
    ElMessage.error('获取每日精选数据失败')
  }
}

// 获取轮播图数据
const fetchBanners = async () => {
  try {
    const response = await getBanners()
    lunbo.value = response.data?.lunbo || []
    if (lunbo.value.length > 0) {
      startAutoSlide()
    }
  } catch (error) {
    ElMessage.error('获取轮播图数据失败')
  }
}

// 获取个性需求数据
const fetchCustomRequirements = async () => {
  if (!isLoggedIn.value) return

  try {
    const response = await getCustomRequirements()
    dingzhi.value = response.data?.dingzhi || []
  } catch (error) {
    ElMessage.error('获取个性需求数据失败')
  }
}

// 删除个性需求 - 完全按照PHP原版
const deleteDingzhi = async (id: number) => {
  try {
    await ElMessageBox.confirm('确认删除这个需求吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteCustomRequirement(id)
    ElMessage.success('删除成功')
    fetchCustomRequirements() // 重新获取数据
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchHomepageData()
  fetchBanners()
  if (isLoggedIn.value) {
    fetchCustomRequirements()
  }
})

// 页面卸载时清理定时器
onUnmounted(() => {
  stopAutoSlide()
})
</script>

<style scoped>
.home-page {
  background-color: #f2f2f2;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 标题栏样式 */
.bar {
  background: #28afe5;
  color: #fff;
  padding: 15px 20px;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
}

/* 表格样式 */
.chaoshi_tbl {
  border-collapse: collapse;
  border: 1px solid #e4e4e4;
}

.chaoshi_tbl th,
.chaoshi_tbl td {
  border: 1px solid #e4e4e4;
  padding: 12px 8px;
  text-align: center;
}

.chaoshi_tbl th {
  background: #28afe5;
  color: #fff;
  font-weight: bold;
}

.chaoshi_tbl tr:nth-child(even) {
  background: #f9f9f9;
}

.chaoshi_tbl tr:hover {
  background: #f0f8ff;
}

/* 轮播图样式 */
.banner-section {
  margin: 20px 0;
}

.banner-item {
  height: 450px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-link {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: block;
}

.banner-placeholder {
  text-align: center;
  color: #fff;
  z-index: 1;
  position: relative;
}

.banner-placeholder h2 {
  font-size: 36px;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.banner-placeholder p {
  font-size: 18px;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.no-banners {
  height: 450px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 个性需求样式 */
.custom-requirements {
  background-color: #f2f2f2;
  padding: 20px 0;
}

.requirement-item {
  background-color: #fff;
  border: 1px solid #ddd;
  padding: 20px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.requirement-content {
  flex: 1;
  margin-right: 20px;
}

.requirement-header {
  margin-bottom: 10px;
}

.requirement-no {
  font-weight: bold;
  margin-right: 10px;
}

.requirement-info {
  color: #999;
  margin-right: 10px;
}

.requirement-info strong {
  color: #333;
}

.requirement-details {
  line-height: 30px;
}

.detail-item {
  color: #999;
  margin-right: 10px;
  display: inline-block;
}

.detail-item strong {
  color: #333;
}

/* 轮播图样式 */
.lunbo_box {
  width: 100%;
  height: 400px;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.lunbo_list {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease;
}

.lunbo_item {
  min-width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}

.lunbo_link {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.lunbo_dots {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 3;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background 0.3s;
}

.dot.active {
  background: #28afe5;
}

.lunbo_prev,
.lunbo_next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  border: none;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
  transition: background 0.3s;
}

.lunbo_prev:hover,
.lunbo_next:hover {
  background: rgba(0, 0, 0, 0.7);
}

.lunbo_prev {
  left: 20px;
}

.lunbo_next {
  right: 20px;
}

/* 个性需求样式 */
.dingzhi_item {
  border: 1px solid #e4e4e4;
  border-radius: 8px;
  transition: box-shadow 0.3s;
}

.dingzhi_item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chaoshi_tbl {
    font-size: 12px;
  }

  .chaoshi_tbl th,
  .chaoshi_tbl td {
    padding: 8px 4px;
  }

  .lunbo_box {
    height: 250px;
  }

  .lunbo_prev,
  .lunbo_next {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .dingzhi_item .fl,
  .dingzhi_item .fr {
    float: none !important;
    width: 100% !important;
  }

  .dingzhi_item .fr {
    margin-top: 15px;
    text-align: left !important;
  }
}
</style>
