<template>
  <div class="public-layout">
    <!-- 头部导航 -->
    <header class="header">
      <div class="container header-container">
        <div class="logo">
          <router-link to="/public/home">
            <img src="@/assets/logo.svg" alt="棉花棠" class="logo-img" />
            <span class="logo-text">棉花棠</span>
          </router-link>
        </div>
        <nav class="nav">
          <ul class="nav-list">
            <li class="nav-item">
              <router-link to="/public/home" :class="{ active: isActive('/public/home') }">每日精选</router-link>
            </li>
            <li class="nav-item">
              <router-link to="/public/products" :class="{ active: isActive('/public/products') }">棉花商城</router-link>
            </li>
            <li class="nav-item">
              <router-link to="/public/custom-orders" :class="{ active: isActive('/public/custom-orders') }">个性需求</router-link>
            </li>
            <li class="nav-item">
              <router-link to="/public/favorites" :class="{ active: isActive('/public/favorites') }">我的收藏</router-link>
            </li>
          </ul>
        </nav>
        <div class="user-actions">
          <template v-if="isLoggedIn">
            <el-dropdown trigger="click" @command="handleCommand">
              <span class="user-info">
                {{ username }}
                <el-icon><CaretBottom /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                  <el-dropdown-item command="orders">我的订单</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else>
            <router-link to="/login" class="login-btn">登录</router-link>
            <router-link to="/register" class="register-btn">注册</router-link>
          </template>
        </div>
      </div>
    </header>

    <!-- 内容区域 -->
    <main class="main-content">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>

    <!-- 底部 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-top">
          <div class="footer-section">
            <h3>联系我们</h3>
            <p><i class="el-icon-location"></i> 新疆乌鲁木齐市沙依巴克区西北路</p>
            <p><i class="el-icon-phone"></i> 0991-123456789</p>
            <p><i class="el-icon-message"></i> <EMAIL></p>
          </div>
          <div class="footer-section">
            <h3>快速链接</h3>
            <ul>
              <li><router-link to="/public/home">每日精选</router-link></li>
              <li><router-link to="/public/products">棉花商城</router-link></li>
              <li><router-link to="/public/custom-orders">个性需求</router-link></li>
              <li><router-link to="/public/favorites">我的收藏</router-link></li>
            </ul>
          </div>
          <div class="footer-section">
            <h3>关注我们</h3>
            <div class="social-icons">
              <a href="#" class="social-icon"><i class="el-icon-s-custom"></i></a>
              <a href="#" class="social-icon"><i class="el-icon-s-promotion"></i></a>
              <a href="#" class="social-icon"><i class="el-icon-s-platform"></i></a>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; {{ currentYear }} 棉花棠. 保留所有权利</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { CaretBottom } from '@element-plus/icons-vue'
import { logout, getUserInfo } from '@/api/auth'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 用户信息
const isLoggedIn = computed(() => userStore.isLoggedIn)
const username = computed(() => userStore.userInfo.nickname || userStore.userInfo.username || '用户')

// 获取当前年份
const currentYear = computed(() => new Date().getFullYear())

// 判断当前路由是否激活
const isActive = (path: string) => {
  return route.path === path
}

// 处理下拉菜单命令
const handleCommand = async (command: string) => {
  if (command === 'logout') {
    try {
      // 调用退出登录API
      await logout()
      userStore.logout()
      ElMessage({
        type: 'success',
        message: '已退出登录'
      })
      router.push('/login')
    } catch (error) {
      ElMessage.error('退出登录失败')
    }
  } else if (command === 'profile') {
    router.push('/user/profile')
  } else if (command === 'orders') {
    router.push('/user/orders')
  }
}

// 检查登录状态
const checkLoginStatus = async () => {
  const token = localStorage.getItem('token')
  if (token) {
    try {
      // 调用API验证token并获取用户信息
      const response = await getUserInfo()
      userStore.setUserInfo(response.data)
    } catch (error) {
      // token无效，清除本地存储
      userStore.logout()
    }
  }
}

// 页面加载时检查登录状态
onMounted(() => {
  checkLoginStatus()
})
</script>

<style scoped>
.public-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  width: 1200px;
  max-width: 100%;
  padding: 0 15px;
  margin: 0 auto;
}

/* 头部样式 */
.header {
  background-color: #000;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.logo a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #fff;
}

.logo-img {
  height: 40px;
  margin-right: 10px;
}

.logo-text {
  font-size: 24px;
  font-weight: bold;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin: 0 15px;
}

.nav-item a {
  text-decoration: none;
  color: #fff;
  font-size: 16px;
  padding: 8px 15px;
  position: relative;
  display: block;
}

.nav-item a:hover,
.nav-item a.active {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
}

.user-actions {
  display: flex;
  align-items: center;
}

.login-btn,
.register-btn {
  text-decoration: none;
  margin-left: 15px;
}

.login-btn {
  color: #409EFF;
}

.register-btn {
  background-color: #409EFF;
  color: #fff;
  padding: 6px 15px;
  border-radius: 4px;
}

.user-info {
  cursor: pointer;
  display: flex;
  align-items: center;
}

/* 内容区域样式 */
.main-content {
  flex: 1;
  background-color: #f5f7fa;
  padding: 20px 0;
}

/* 底部样式 */
.footer {
  background-color: #333;
  color: #fff;
  padding: 40px 0 20px;
}

.footer-top {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 30px;
}

.footer-section {
  width: 30%;
}

.footer-section h3 {
  position: relative;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.footer-section h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 40px;
  height: 2px;
  background-color: #409EFF;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: 10px;
}

.footer-section ul li a {
  color: #ddd;
  text-decoration: none;
}

.footer-section ul li a:hover {
  color: #409EFF;
}

.social-icons {
  display: flex;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin-right: 10px;
  background-color: rgba(255,255,255,0.1);
  border-radius: 50%;
  color: #fff;
  text-decoration: none;
}

.social-icon:hover {
  background-color: #409EFF;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255,255,255,0.1);
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
