<template>
  <div class="public-layout">
    <!-- 头部导航 - 完全按照PHP原版 -->
    <div class="bg_b min_w">
      <div class="m_auto clearfix">
        <ul class="nav_ul clearfix f_16 fl">
          <li><router-link id="n1" to="/public/home" class="clr_f" :class="{ nav_cur: isActive('/public/home') }">每日精选</router-link></li>
          <li><router-link id="n2" to="/public/products" class="clr_f" :class="{ nav_cur: isActive('/public/products') }">棉花商城</router-link></li>
          <li><router-link id="n3" to="/public/custom-orders" class="clr_f" :class="{ nav_cur: isActive('/public/custom-orders') }">个性需求</router-link></li>
          <li><router-link id="n4" to="/public/favorites" class="clr_f" :class="{ nav_cur: isActive('/public/favorites') }">我的收藏</router-link></li>
        </ul>

        <div class="f_16 clr_f nav_time fr">
          <template v-if="isLoggedIn">
            <span>欢迎您，{{ username }}！</span>
            <a @click="handleLogout" class="clr_f ml_20 pointer">退出登录</a>
          </template>
          <template v-else>
            <span>欢迎您，今天是：{{ currentDate }}</span>
            <router-link to="/login" class="clr_f ml_20">登录</router-link>
            <router-link to="/register" class="clr_f ml_20">注册</router-link>
          </template>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <main class="main-content">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>

    <!-- 底部 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-top">
          <div class="footer-section">
            <h3>联系我们</h3>
            <p><i class="el-icon-location"></i> 新疆乌鲁木齐市沙依巴克区西北路</p>
            <p><i class="el-icon-phone"></i> 0991-123456789</p>
            <p><i class="el-icon-message"></i> <EMAIL></p>
          </div>
          <div class="footer-section">
            <h3>快速链接</h3>
            <ul>
              <li><router-link to="/public/home">每日精选</router-link></li>
              <li><router-link to="/public/products">棉花商城</router-link></li>
              <li><router-link to="/public/custom-orders">个性需求</router-link></li>
              <li><router-link to="/public/favorites">我的收藏</router-link></li>
            </ul>
          </div>
          <div class="footer-section">
            <h3>关注我们</h3>
            <div class="social-icons">
              <a href="#" class="social-icon"><i class="el-icon-s-custom"></i></a>
              <a href="#" class="social-icon"><i class="el-icon-s-promotion"></i></a>
              <a href="#" class="social-icon"><i class="el-icon-s-platform"></i></a>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; {{ currentYear }} 棉花棠. 保留所有权利</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { CaretBottom } from '@element-plus/icons-vue'
import { logout, getUserInfo } from '@/api/auth'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 用户信息
const isLoggedIn = computed(() => userStore.isLoggedIn)
const username = computed(() => userStore.userInfo.nickname || userStore.userInfo.username || '用户')

// 获取当前年份和日期
const currentYear = computed(() => new Date().getFullYear())
const currentDate = computed(() => {
  const now = new Date()
  return `${now.getFullYear()}年${(now.getMonth() + 1).toString().padStart(2, '0')}月${now.getDate().toString().padStart(2, '0')}日`
})

// 判断当前路由是否激活
const isActive = (path: string) => {
  return route.path === path || route.path.startsWith(path)
}

// 处理退出登录
const handleLogout = async () => {
  try {
    // 调用退出登录API
    await logout()
    userStore.logout()
    ElMessage({
      type: 'success',
      message: '已退出登录'
    })
    router.push('/login')
  } catch (error) {
    ElMessage.error('退出登录失败')
  }
}

// 检查登录状态
const checkLoginStatus = async () => {
  const token = localStorage.getItem('token')
  if (token) {
    try {
      // 调用API验证token并获取用户信息
      const response = await getUserInfo()
      userStore.setUserInfo(response.data)
    } catch (error) {
      // token无效，清除本地存储
      userStore.logout()
    }
  }
}

// 页面加载时检查登录状态
onMounted(() => {
  checkLoginStatus()
})
</script>

<style scoped>
.public-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  width: 1200px;
  max-width: 100%;
  padding: 0 15px;
  margin: 0 auto;
}

/* 导航栏样式 - 完全按照PHP原版 */
.min_w {
  min-width: 1200px;
}

.nav_ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
}

.nav_ul li {
  margin-right: 30px;
}

.nav_ul li a {
  display: block;
  padding: 15px 20px;
  text-decoration: none;
  transition: background-color 0.3s;
}

.nav_ul li a:hover,
.nav_ul li a.nav_cur {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav_time {
  padding: 15px 20px;
  line-height: 1.5;
}

/* 内容区域样式 */
.main-content {
  flex: 1;
  background-color: #f2f2f2;
  min-height: calc(100vh - 120px);
}

/* 底部样式 */
.footer {
  background-color: #333;
  color: #fff;
  padding: 40px 0 20px;
}

.footer-top {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 30px;
}

.footer-section {
  width: 30%;
}

.footer-section h3 {
  position: relative;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.footer-section h3::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 40px;
  height: 2px;
  background-color: #409EFF;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: 10px;
}

.footer-section ul li a {
  color: #ddd;
  text-decoration: none;
}

.footer-section ul li a:hover {
  color: #409EFF;
}

.social-icons {
  display: flex;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin-right: 10px;
  background-color: rgba(255,255,255,0.1);
  border-radius: 50%;
  color: #fff;
  text-decoration: none;
}

.social-icon:hover {
  background-color: #409EFF;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255,255,255,0.1);
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
