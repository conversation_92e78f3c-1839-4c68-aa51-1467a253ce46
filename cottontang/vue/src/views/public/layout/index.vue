<template>
  <div class="public-layout">
    <!-- 顶部导航组件 -->
    <TopNavigation />

    <!-- 主导航 - 完全按照PHP原版 -->
    <div class="bg_b min_w">
      <div class="m_auto clearfix">
        <ul class="nav_ul clearfix f_16 fl">
          <li><router-link id="n1" to="/public/home" class="clr_f" :class="{ nav_cur: isActive('/public/home') }">每日精选</router-link></li>
          <li><router-link id="n2" to="/public/products" class="clr_f" :class="{ nav_cur: isActive('/public/products') }">棉花商城</router-link></li>
          <li><router-link id="n3" to="/public/custom-orders" class="clr_f" :class="{ nav_cur: isActive('/public/custom-orders') }">个性需求</router-link></li>
          <li><router-link id="n4" to="/public/favorites" class="clr_f" :class="{ nav_cur: isActive('/public/favorites') }">我的收藏</router-link></li>
        </ul>

        <div class="f_16 clr_f nav_time fr">欢迎您，今天是：{{ currentDate }}</div>
      </div>
    </div>

    <!-- 内容区域 -->
    <main class="main-content">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>

    <!-- 底部 - 完全按照PHP原版 -->
    <div class="f_14 clr_9 al_ct padt_10 lh_28">
      版权所有：青岛三匹马实业有限公司&nbsp;&nbsp;&nbsp;&nbsp;地址：青岛市黄岛区江山南路450号（富安国际大厦）&nbsp;&nbsp;&nbsp;&nbsp;电话：17866631857<br/>
      Copyright &copy; 2021 鲁ICP备2021017032号-1
    </div>

    <!-- 右侧悬浮工具栏 - 完全按照PHP原版 -->
    <div class="side">
      <ul>
        <li class="sideewm">
          <i class="bgs3"></i>官方微信
          <div class="ewBox son"></div>
        </li>
        <li class="sideetel">
          <i class="bgs4"></i>联系电话
          <div class="telBox son">
            <dd class="bgs2"><span>手机</span>19853227218</dd>
          </div>
        </li>
        <li class="sidetop" @click="goTop">
          <i class="bgs6"></i>返回顶部
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getUserInfo } from '@/api/auth'
import { useUserStore } from '@/stores/user'
import TopNavigation from '@/components/TopNavigation.vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 用户信息
const isLoggedIn = computed(() => userStore.isLoggedIn)
const username = computed(() => userStore.userInfo.nickname || userStore.userInfo.username || '用户')

// 获取当前年份和日期 - 完全按照PHP原版
const currentYear = computed(() => new Date().getFullYear())
const currentDate = computed(() => {
  const now = new Date()
  return `${now.getFullYear()}年${(now.getMonth() + 1).toString().padStart(2, '0')}月${now.getDate().toString().padStart(2, '0')}日`
})

// 判断当前路由是否激活 - 完全按照PHP原版逻辑
const isActive = (path: string) => {
  const currentPath = route.path
  if (path === '/public/home') {
    return currentPath === '/public/home' || currentPath === '/'
  } else if (path === '/public/products') {
    return currentPath.includes('products') || currentPath.includes('Chaoshi')
  } else if (path === '/public/custom-orders') {
    return currentPath.includes('custom-orders') || currentPath.includes('dingzhi')
  } else if (path === '/public/favorites') {
    return currentPath.includes('favorites') || currentPath.includes('favor')
  }
  return currentPath === path
}

// 返回顶部功能 - 完全按照PHP原版
const goTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 不需要下拉菜单，PHP原版没有

// 检查登录状态
const checkLoginStatus = async () => {
  const token = localStorage.getItem('token')
  if (token) {
    try {
      // 调用API验证token并获取用户信息
      const response = await getUserInfo()
      userStore.setUserInfo(response.data)
    } catch (error) {
      // token无效，清除本地存储
      userStore.logout()
    }
  }
}

// 页面加载时检查登录状态
onMounted(() => {
  checkLoginStatus()
})
</script>

<style scoped>
.public-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 - 使用全局CSS，只添加必要的补充样式 */

/* 导航链接的悬停和激活状态 */
.nav_ul li a {
  text-decoration: none;
  color: #fff;
  transition: background-color 0.3s;
}

.nav_ul li a:hover,
.nav_ul li a.nav_cur {
  background: #f1830e;
}

/* 确保最小宽度 */
.min_w {
  min-width: 1200px;
}

/* 内容区域样式 */
.main-content {
  flex: 1;
  background-color: #f2f2f2;
  min-height: calc(100vh - 120px);
}

/* 右侧悬浮工具栏样式 - 完全按照PHP原版 */
.side {
  position: fixed;
  width: 60px;
  right: 0;
  top: 60%;
  margin-top: -200px;
  z-index: 100;
  border: 1px solid #e0e0e0;
  background: #fff;
  border-bottom: 0;
}

.side ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.side ul li {
  width: 60px;
  height: 70px;
  float: left;
  position: relative;
  border-bottom: 1px solid #e0e0e0;
  color: #333;
  font-size: 14px;
  line-height: 38px;
  text-align: center;
  transition: all 0.3s;
  cursor: pointer;
}

.side ul li:hover {
  background: #f67524;
  color: #fff;
}

.side ul li:hover a {
  color: #fff;
}

.side ul li i {
  height: 25px;
  margin-bottom: 1px;
  display: block;
  overflow: hidden;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: auto 25px;
  margin-top: 14px;
  transition: all 0.3s;
}

.side ul li i.bgs1 {
  background-image: url(/images/right_pic5.png);
}

.side ul li i.bgs2 {
  background-image: url(/images/right_pic7.png);
}

.side ul li i.bgs3 {
  background-image: url(/images/right_pic2.png);
}

.side ul li i.bgs4 {
  background-image: url(/images/right_pic1.png);
}

.side ul li i.bgs5 {
  background-image: url(/images/right_pic3.png);
}

.side ul li i.bgs6 {
  background-image: url(/images/right_pic6_on.png);
}

.side ul li:hover i.bgs1 {
  background-image: url(/images/right_pic5_on.png);
}

.side ul li:hover i.bgs2 {
  background-image: url(/images/right_pic7_on.png);
}

.side ul li:hover i.bgs3 {
  background-image: url(/images/right_pic2_on.png);
}

.side ul li:hover i.bgs4 {
  background-image: url(/images/right_pic1_on.png);
}

.side ul li:hover i.bgs5 {
  background-image: url(/images/right_pic3_on.png);
}

.side ul li .sidebox {
  position: absolute;
  width: 78px;
  height: 78px;
  top: 0;
  right: 0;
  transition: all 0.3s;
  overflow: hidden;
}

.side ul li.sidetop {
  background: #f67524;
  color: #fff;
}

.side ul li.sidetop:hover {
  opacity: 0.8;
  filter: Alpha(opacity=80);
}

.side ul li.sideewm .ewBox.son {
  width: 238px;
  display: none;
  color: #363636;
  text-align: center;
  padding-top: 212px;
  position: absolute;
  left: -240px;
  top: 0;
  background-image: url(/images/leftewm.png);
  background-repeat: no-repeat;
  background-position: center center;
  border: 1px solid #e0e0e0;
}

.side ul li.sideetel .telBox.son {
  width: 240px;
  height: 214px;
  display: none;
  color: #fff;
  text-align: left;
  position: absolute;
  left: -240px;
  top: -72px;
  background: #f67524;
}

.side ul li.sideetel .telBox dd {
  display: block;
  height: 118.5px;
  overflow: hidden;
  padding-left: 82px;
  line-height: 24px;
  font-size: 18px;
}

.side ul li.sideetel .telBox dd span {
  display: block;
  line-height: 28px;
  height: 28px;
  overflow: hidden;
  margin-top: 32px;
  font-size: 18px;
}

.side ul li.sideetel .telBox dd.bgs1 {
  background: url(/images/right_pic8.png) 28px center no-repeat;
  background-color: #e96410;
}

.side ul li.sideetel .telBox dd.bgs2 {
  background: url(/images/right_pic9.png) 28px center no-repeat;
}

.side ul li:hover .son {
  display: block !important;
  animation: fadein 1s;
}

@keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
