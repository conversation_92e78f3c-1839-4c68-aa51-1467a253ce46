<template>
  <div class="register-page">
    <div class="bg_f min_w">
      <div class="m_auto padt_15 clearfix">
        <div class="fl">
          <router-link to="/public/home">
            <img src="/images/logo.png" class="dis_b" style="height:78px;" />
          </router-link>
        </div>
        <div class="fl ml_50 pos_rela">
          <div style="position: absolute;top:55px;left:0;" class="dis_n" id="pl_box" ref="plBox">
            <textarea
              style="width:400px;height: 150px;"
              id="pl_text"
              v-model="batchText"
              placeholder="每行输入一个批次/捆号"
              class="border1 dis_b pad_10"
            ></textarea>
            <input
              type="button"
              id="pl_sou"
              value="搜索"
              class="dis_b bg_ora"
              style="color: #fff;padding:5px 10px;border:0"
              @click="handleBatchSearch"
            />
          </div>
          <form action="#" id="ss_fm" style="margin-top:9px;" @submit.prevent="handleSearch">
            <input
              type="search"
              name="sk"
              id="sousuo_sk"
              v-model="searchKeyword"
              class="clr_9"
              autocomplete="off"
              placeholder="批号/轧花厂/仓库/产棉地区"
              style="height: 40px;border:2px solid #28afe5;width:500px;padding:10px;box-sizing: border-box;"
            >
            <button type="submit" class="send" style="height: 40px;outline: none;cursor: pointer;">
              <img src="/images/searth.png" style="width:20px;">
            </button>
            <span
              class="f_14 clr_r pointer"
              id="pl_click"
              style="position: absolute;top:30px;right:80px;"
              @click="toggleBatchBox"
            >批量</span>
          </form>
        </div>
        <div class="fr al_rt f_14 login">
          <div class="mt_10">
            <router-link to="/register" class="clr_d f_16">注册</router-link>
            <router-link to="/login" class="clr_d f_16 log_a">登录</router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- 注册区域 -->
    <div class="m_auto border1 reg_bg">
      <div class="pad_20"></div>
      <div class="reg_box">
        <form id="fm1" @submit.prevent="handleRegister">
          <div class="border1 pad_10 mt_20">
            <img src="/images/mobile.png" class="ver_mid" />
            <input
              class="f_14 clr_9 border0 ver_mid ml_10"
              name="username"
              id="username"
              v-model="registerForm.username"
              placeholder="手机号"
            />
          </div>
          <div class="border1 pad_10 mt_20 pos_rela">
            <img src="/images/mobile.png" class="ver_mid" />
            <input
              class="f_14 clr_9 border0 ver_mid ml_10"
              name="mobile_code"
              v-model="registerForm.mobileCode"
              placeholder="手机验证码"
              style="width:100px;"
            />
            <input
              type="button"
              :value="codeCountdown > 0 ? `${codeCountdown}秒` : '获取验证码'"
              class="bg_b clr_f border0 pad_10"
              id="getcode"
              :disabled="codeCountdown > 0"
              style="position: absolute;top:10px;right:10px;"
              @click="sendCode"
            />
          </div>
          <div class="border1 pad_10 mt_20">
            <img src="/images/user.png" class="ver_mid" />
            <input
              class="f_14 clr_9 border0 ver_mid ml_10"
              name="nickname"
              id="nickname"
              v-model="registerForm.nickname"
              placeholder="姓名"
            />
          </div>
          <div class="border1 pad_10 mt_20">
            <img src="/images/gsmc.png" class="ver_mid" />
            <input
              class="f_14 clr_9 border0 ver_mid ml_10"
              name="company"
              id="company"
              v-model="registerForm.company"
              placeholder="公司名称"
            />
          </div>
          <div class="border1 pad_10 mt_20">
            <img src="/images/password.png" class="ver_mid" />
            <input
              class="f_14 clr_9 border0 ver_mid ml_10"
              name="password"
              id="password"
              v-model="registerForm.password"
              type="password"
              placeholder="密码"
            />
          </div>
          <div class="border1 pad_10 mt_20">
            <img src="/images/password.png" class="ver_mid" />
            <input
              class="f_14 clr_9 border0 ver_mid ml_10"
              style="background: transparent;"
              name="password2"
              id="password2"
              v-model="registerForm.password2"
              type="password"
              placeholder="重复密码"
            />
          </div>
          <div class="mt_10">
            <label>
              <input type="checkbox" class="ver_mid" id="agreement" v-model="registerForm.agreement" />
              <span class="ver_mid f_14 clr_9 ver_mid">已阅读并同意 <a href="#" class="clr_b" @click="showAgreement">《用户服务协议》</a></span>
            </label>
          </div>
          <div class="padt_30">
            <input
              type="submit"
              value="注册"
              :disabled="loading"
              class="f_16 clr_f bg_b w_100p border0 padt_10"
            />
            <div class="f_14 al_rt mt_10">已有账号，<router-link to="/login" class="clr_b">立即登录</router-link></div>
          </div>
        </form>
      </div>
      <div class="pad_20"></div>
    </div>

    <!-- 底部 -->
    <div class="min_w">
      <div class="f_14 clr_9 al_ct padt_10 lh_28 footer-content">
        版权所有：青岛三匹马实业有限公司&nbsp;&nbsp;&nbsp;&nbsp;地址：青岛市黄岛区江山南路450号（富安国际大厦）&nbsp;&nbsp;&nbsp;&nbsp;电话：17866631857<br/>
        Copyright &copy; 2021 鲁ICP备2021017032号-1
      </div>
    </div>

    <!-- 侧边栏 -->
    <div class="side">
      <ul>
        <li class="sideewm">
          <i class="bgs3"></i>官方微信
          <div class="ewBox son"></div>
        </li>
        <li class="sideetel">
          <i class="bgs4"></i>联系电话
          <div class="telBox son">
            <dd class="bgs2"><span>手机</span>19853227218</dd>
          </div>
        </li>
        <li class="sidetop" @click="goTop">
          <i class="bgs6"></i>返回顶部
        </li>
      </ul>
    </div>

    <!-- 用户协议对话框 -->
    <div v-if="agreementVisible" class="agreement-modal" @click="closeAgreement">
      <div class="agreement-content" @click.stop>
        <h3>用户服务协议</h3>
        <div class="agreement-text">
          <p>欢迎使用棉花棠交易平台！</p>
          <p>在使用本平台服务前，请仔细阅读以下用户协议：</p>
          <ol>
            <li>用户应提供真实、准确的注册信息</li>
            <li>用户应妥善保管账号和密码</li>
            <li>禁止发布虚假交易信息</li>
            <li>遵守国家相关法律法规</li>
            <li>平台有权对违规行为进行处理</li>
          </ol>
          <p>如有疑问，请联系客服。</p>
        </div>
        <div class="agreement-footer">
          <button @click="closeAgreement" class="bg_b clr_f border0 pad_10">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { register, sendSmsCode } from '@/api/auth'

const router = useRouter()

// 搜索相关数据
const searchKeyword = ref('')
const batchText = ref('')
const plBox = ref<HTMLElement>()

// 注册表单数据
const loading = ref(false)
const codeCountdown = ref(0)
const agreementVisible = ref(false)

const registerForm = reactive({
  username: '',
  mobileCode: '',
  nickname: '',
  company: '',
  password: '',
  password2: '',
  agreement: false
})

// 搜索相关方法 - 完全按照PHP的jQuery逻辑
const toggleBatchBox = () => {
  if (plBox.value) {
    const currentDisplay = plBox.value.style.display
    if (currentDisplay === 'none' || !currentDisplay) {
      plBox.value.style.display = 'block'
      plBox.value.classList.remove('dis_n')
      plBox.value.classList.add('dis_b')
    } else {
      plBox.value.style.display = 'none'
      plBox.value.classList.remove('dis_b')
      plBox.value.classList.add('dis_n')
    }
  }
}

const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    console.log('搜索关键词:', searchKeyword.value)
  }
}

const handleBatchSearch = () => {
  const ss = batchText.value.trim()
  const arr = ss.split(/\n/g)
  const xx: string[] = []
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].trim() !== '') {
      xx.push(arr[i].trim())
    }
  }
  searchKeyword.value = xx.toString()
  handleSearch()
}

// 发送验证码 - 完全按照PHP的jQuery逻辑
const sendCode = async () => {
  // 验证手机号
  if (!/^1\d{10}$/.test(registerForm.username)) {
    alert('请输入正确的手机号')
    return false
  }

  try {
    await sendSmsCode(registerForm.username)
    alert('验证码已发送')
    time()
  } catch (error: any) {
    alert(error.message || '验证码发送失败')
  }
}

// 倒计时函数 - 完全按照PHP的JavaScript逻辑
let wait = 120
function time() {
  if (wait == 0) {
    codeCountdown.value = 0
    wait = 120
  } else {
    codeCountdown.value = wait
    wait--
    setTimeout(function() {
      time()
    }, 1000)
  }
}

// 显示用户协议
const showAgreement = (e: Event) => {
  e.preventDefault()
  agreementVisible.value = true
}

// 关闭用户协议
const closeAgreement = () => {
  agreementVisible.value = false
}

// 处理注册 - 完全按照PHP的jQuery逻辑
const handleRegister = async (e: Event) => {
  e.preventDefault()

  // 验证手机号
  if (!/^1\d{10}$/.test(registerForm.username)) {
    alert('请输入正确的手机号码')
    const usernameEl = document.getElementById('username') as HTMLInputElement
    usernameEl?.focus()
    return false
  }

  // 验证姓名
  if (/^[ ]*$/.test(registerForm.nickname)) {
    alert('请输入姓名')
    const nicknameEl = document.getElementById('nickname') as HTMLInputElement
    nicknameEl?.focus()
    return false
  }

  // 验证公司名称
  if (/^[ ]*$/.test(registerForm.company)) {
    alert('请输入公司名称')
    const companyEl = document.getElementById('company') as HTMLInputElement
    companyEl?.focus()
    return false
  }

  // 验证密码
  if (/^[ ]*$/.test(registerForm.password)) {
    alert('请输入密码')
    const passwordEl = document.getElementById('password') as HTMLInputElement
    passwordEl?.focus()
    return false
  }

  // 验证密码一致性
  if (registerForm.password2 != registerForm.password) {
    alert('密码输入不一致')
    const password2El = document.getElementById('password2') as HTMLInputElement
    password2El?.focus()
    return false
  }

  // 验证协议
  if (!registerForm.agreement) {
    alert('请阅读并同意《用户服务协议》')
    return false
  }

  try {
    loading.value = true

    const registerData = {
      username: registerForm.username,
      password: registerForm.password,
      mobile_code: registerForm.mobileCode,
      nickname: registerForm.nickname,
      company: registerForm.company
    }

    const response = await register(registerData)
    alert(response.msg || '注册成功')
    if (response.url) {
      router.push(response.url)
    } else {
      router.push('/login')
    }
  } catch (error: any) {
    alert(error.message || '注册失败')
  } finally {
    loading.value = false
  }
}

// 返回顶部
const goTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 页面加载时的初始化
onMounted(() => {
  // 初始化代码
})
</script>

<style scoped>

/* 页面整体 */
.register-page {
  min-height: 100vh;
  font-family: "Microsoft YaHei", Arial, sans-serif;
}


/* 用户协议弹窗 */
.agreement-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.agreement-content {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
}

.agreement-content h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.agreement-text {
  line-height: 1.6;
  margin-bottom: 20px;
}

.agreement-text ol {
  padding-left: 20px;
}

.agreement-text li {
  margin-bottom: 8px;
}

.agreement-footer {
  text-align: center;
}

.agreement-footer button {
  cursor: pointer;
  border-radius: 4px;
}

/* 侧边栏样式 */
.side {
  position: fixed;
  width: 60px;
  right: 0;
  top: 60%;
  margin-top: -200px;
  z-index: 100;
  border: 1px solid #e0e0e0;
  background: #fff;
  border-bottom: 0;
}

.side ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.side ul li {
  width: 60px;
  height: 70px;
  float: left;
  position: relative;
  border-bottom: 1px solid #e0e0e0;
  color: #333;
  font-size: 14px;
  line-height: 38px;
  text-align: center;
  transition: all 0.3s;
  cursor: pointer;
}

.side ul li:hover {
  background: #f67524;
  color: #fff;
}

.side ul li i {
  height: 25px;
  margin-bottom: 1px;
  display: block;
  overflow: hidden;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: auto 25px;
  margin-top: 14px;
  transition: all 0.3s;
}

.side ul li i.bgs3 {
  background-image: url('/images/right_pic2.png');
}

.side ul li i.bgs4 {
  background-image: url('/images/right_pic1.png');
}

.side ul li i.bgs6 {
  background-image: url('/images/right_pic6_on.png');
}

.side ul li.sidetop {
  background: #f67524;
  color: #fff;
}

.side ul li.sidetop:hover {
  opacity: 0.8;
}

.side ul li.sideewm .ewBox.son {
  width: 238px;
  display: none;
  color: #363636;
  text-align: center;
  padding-top: 212px;
  position: absolute;
  left: -240px;
  top: 0;
  background-image: url('/images/leftewm.png');
  background-repeat: no-repeat;
  background-position: center center;
  border: 1px solid #e0e0e0;
}

.side ul li.sideetel .telBox.son {
  width: 240px;
  height: 214px;
  display: none;
  color: #fff;
  text-align: left;
  position: absolute;
  left: -240px;
  top: -72px;
  background: #f67524;
}

.side ul li.sideetel .telBox dd {
  display: block;
  height: 118.5px;
  overflow: hidden;
  padding-left: 82px;
  line-height: 24px;
  font-size: 18px;
  margin: 0;
}

.side ul li.sideetel .telBox dd span {
  display: block;
  line-height: 28px;
  height: 28px;
  overflow: hidden;
  margin-top: 32px;
  font-size: 18px;
}

.side ul li.sideetel .telBox dd.bgs2 {
  background: url('/images/right_pic9.png') 28px center no-repeat;
}

.side ul li:hover .son {
  display: block !important;
  animation: fadein 1s;
}

@keyframes fadein {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 响应式 */
@media (max-width: 1200px) {
  .min_w {
    min-width: 100%;
  }

  .m_auto {
    min-width: 100%;
    width: 100%;
    padding: 0 20px;
  }

  .reg_bg {
    width: 100%;
    max-width: 380px;
    margin: 20px auto;
  }
}

@media (max-width: 768px) {
  .reg_box input:not([type="submit"]):not([type="checkbox"]):not([type="button"]) {
    width: 200px;
  }

  .reg_box {
    padding: 0 20px;
  }
}
</style>
