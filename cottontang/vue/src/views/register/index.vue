<template>
  <div class="register-page">
    <div class="register-container">
      <div class="register-header">
        <h1>用户注册</h1>
        <p>欢迎注册棉花糖交易平台</p>
      </div>
      
      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        class="register-form"
        label-width="100px"
      >
        <el-form-item label="手机号" prop="username">
          <el-input
            v-model="registerForm.username"
            placeholder="请输入手机号"
            maxlength="11"
          />
        </el-form-item>
        
        <el-form-item label="验证码" prop="mobileCode">
          <div class="code-input">
            <el-input
              v-model="registerForm.mobileCode"
              placeholder="请输入验证码"
              maxlength="6"
            />
            <el-button
              :disabled="codeCountdown > 0"
              @click="sendCode"
              class="code-btn"
            >
              {{ codeCountdown > 0 ? `${codeCountdown}s后重发` : '发送验证码' }}
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="真实姓名" prop="realName">
          <el-input
            v-model="registerForm.realName"
            placeholder="请输入真实姓名"
          />
        </el-form-item>
        
        <el-form-item label="公司名称" prop="company">
          <el-input
            v-model="registerForm.company"
            placeholder="请输入公司名称"
          />
        </el-form-item>
        
        <el-form-item prop="agreement">
          <el-checkbox v-model="registerForm.agreement">
            我已阅读并同意
            <el-link type="primary" @click="showAgreement">《用户协议》</el-link>
          </el-checkbox>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleRegister"
            class="register-btn"
          >
            注册
          </el-button>
        </el-form-item>
        
        <div class="login-link">
          已有账号？<router-link to="/login">立即登录</router-link>
        </div>
      </el-form>
    </div>
    
    <!-- 用户协议对话框 -->
    <el-dialog v-model="agreementVisible" title="用户协议" width="600px">
      <div class="agreement-content">
        <p>欢迎使用棉花糖交易平台！</p>
        <p>在使用本平台服务前，请仔细阅读以下用户协议：</p>
        <ol>
          <li>用户应提供真实、准确的注册信息</li>
          <li>用户应妥善保管账号和密码</li>
          <li>禁止发布虚假交易信息</li>
          <li>遵守国家相关法律法规</li>
          <li>平台有权对违规行为进行处理</li>
        </ol>
        <p>如有疑问，请联系客服。</p>
      </div>
      <template #footer>
        <el-button @click="agreementVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElForm } from 'element-plus'
import { register, sendSmsCode } from '@/api/auth'

const router = useRouter()
const registerFormRef = ref<InstanceType<typeof ElForm>>()

// 响应式数据
const loading = ref(false)
const codeCountdown = ref(0)
const agreementVisible = ref(false)

const registerForm = reactive({
  username: '',
  mobileCode: '',
  password: '',
  confirmPassword: '',
  realName: '',
  company: '',
  agreement: false
})

// 表单验证规则
const registerRules = {
  username: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  mobileCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码长度为6位', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  company: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ],
  agreement: [
    {
      validator: (rule: any, value: boolean, callback: Function) => {
        if (!value) {
          callback(new Error('请阅读并同意用户协议'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 发送验证码
const sendCode = async () => {
  if (!registerForm.username) {
    ElMessage.error('请先输入手机号')
    return
  }
  
  if (!/^1[3-9]\d{9}$/.test(registerForm.username)) {
    ElMessage.error('请输入正确的手机号')
    return
  }
  
  try {
    await sendSmsCode(registerForm.username)
    ElMessage.success('验证码已发送')
    
    // 开始倒计时
    codeCountdown.value = 60
    const timer = setInterval(() => {
      codeCountdown.value--
      if (codeCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  } catch (error) {
    ElMessage.error('验证码发送失败')
  }
}

// 显示用户协议
const showAgreement = () => {
  agreementVisible.value = true
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  try {
    await registerFormRef.value.validate()
    loading.value = true
    
    const registerData = {
      username: registerForm.username,
      password: registerForm.password,
      mobile_code: registerForm.mobileCode,
      real_name: registerForm.realName,
      company: registerForm.company
    }
    
    await register(registerData)
    ElMessage.success('注册成功，请等待审核')
    router.push('/login')
  } catch (error: any) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-container {
  background: #fff;
  border-radius: 10px;
  padding: 40px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-header h1 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 28px;
}

.register-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.register-form {
  width: 100%;
}

.code-input {
  display: flex;
  gap: 10px;
}

.code-input .el-input {
  flex: 1;
}

.code-btn {
  white-space: nowrap;
}

.register-btn {
  width: 100%;
  height: 45px;
  font-size: 16px;
}

.login-link {
  text-align: center;
  margin-top: 20px;
  color: #666;
}

.login-link a {
  color: #409EFF;
  text-decoration: none;
}

.login-link a:hover {
  text-decoration: underline;
}

.agreement-content {
  line-height: 1.6;
}

.agreement-content ol {
  padding-left: 20px;
}

.agreement-content li {
  margin-bottom: 8px;
}

@media (max-width: 768px) {
  .register-container {
    padding: 30px 20px;
  }
  
  .code-input {
    flex-direction: column;
  }
}
</style>
