package com.cottontang.dto;

import lombok.Data;

/**
 * 用户信息数据传输对象
 *
 * <AUTHOR>
 */
@Data
public class UserInfoDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 公司名称
     */
    private String company;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 用户类型
     */
    private Integer type;

    /**
     * 会员类型
     */
    private Integer memberType;

    /**
     * 审核状态
     */
    private Integer isCheck;

    /**
     * 用户状态
     */
    private Integer status;

    /**
     * 用户角色
     */
    private String[] roles;

    /**
     * 注册时间
     */
    private Long addTime;

    /**
     * 最后登录时间
     */
    private Long lastLogin;
}