package com.cottontang.dto;

import lombok.Data;

import java.util.List;

/**
 * 用户信息数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class UserInfoDTO {
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 头像URL
     */
    private String avatar;
    
    /**
     * 用户类型
     */
    private Integer type;
    
    /**
     * 用户权限
     */
    private List<String> permissions;
    
    /**
     * 用户角色
     */
    private List<String> roles;
} 