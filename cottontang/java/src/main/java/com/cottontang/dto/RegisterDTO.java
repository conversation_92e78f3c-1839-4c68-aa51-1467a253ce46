package com.cottontang.dto;

import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 用户注册DTO
 *
 * <AUTHOR>
 */
@Data
public class RegisterDTO {

    /**
     * 用户名（手机号）
     */
    @NotBlank(message = "用户名不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "请输入正确的手机号")
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 确认密码
     */
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 邮箱
     */
    @Email(message = "请输入正确的邮箱格式")
    private String email;

    /**
     * 公司名称
     */
    private String company;

    /**
     * 验证码
     */
    private String captcha;
}
