package com.cottontang.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 文章数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class ArticleDTO {
    
    /**
     * 文章ID
     */
    private Long id;
    
    /**
     * 文章标题
     */
    @NotBlank(message = "标题不能为空")
    @Size(max = 200, message = "标题长度不能超过200个字符")
    private String title;
    
    /**
     * 文章分类ID
     */
    @NotNull(message = "分类不能为空")
    private Long catId;
    
    /**
     * 文章内容
     */
    private String content;
    
    /**
     * 文章缩略图
     */
    private String thumbs;
    
    /**
     * 排序ID
     */
    private Integer orderId;
    
    /**
     * 是否推荐（0: 否, 1: 是）
     */
    private Integer isRecom;
    
    /**
     * 文章描述
     */
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;
    
    /**
     * 分类名称（临时字段）
     */
    private String catName;
} 