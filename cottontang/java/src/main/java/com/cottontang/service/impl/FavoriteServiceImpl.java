package com.cottontang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Favorite;
import com.cottontang.mapper.FavoriteMapper;
import com.cottontang.service.FavoriteService;
import org.springframework.stereotype.Service;

/**
 * 收藏服务实现
 * 
 * <AUTHOR>
 */
@Service
public class FavoriteServiceImpl extends ServiceImpl<FavoriteMapper, Favorite> implements FavoriteService {
    
    @Override
    public boolean addFavorite(Long uid, Long favorId, Integer favorType) {
        // 检查是否已收藏
        if (isFavorited(uid, favorId)) {
            return false;
        }
        
        Favorite favorite = new Favorite();
        favorite.setUid(uid);
        favorite.setFavorId(favorId);
        favorite.setFavorType(favorType);
        favorite.setAddTime(System.currentTimeMillis() / 1000);
        
        return this.save(favorite);
    }
    
    @Override
    public boolean removeFavorite(Long uid, Long favorId) {
        LambdaQueryWrapper<Favorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Favorite::getUid, uid)
               .eq(Favorite::getFavorId, favorId);
        
        return this.remove(wrapper);
    }
    
    @Override
    public boolean isFavorited(Long uid, Long favorId) {
        LambdaQueryWrapper<Favorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Favorite::getUid, uid)
               .eq(Favorite::getFavorId, favorId);
        
        return this.count(wrapper) > 0;
    }
    
    @Override
    public Page<Favorite> getUserFavorites(Long uid, Integer favorType, PageQuery pageQuery) {
        Page<Favorite> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        
        LambdaQueryWrapper<Favorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Favorite::getUid, uid);
        
        if (favorType != null) {
            wrapper.eq(Favorite::getFavorType, favorType);
        }
        
        wrapper.orderByDesc(Favorite::getAddTime);
        
        return this.page(page, wrapper);
    }
    
    @Override
    public long getFavoriteCount(Long favorId) {
        LambdaQueryWrapper<Favorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Favorite::getFavorId, favorId);
        
        return this.count(wrapper);
    }
}
