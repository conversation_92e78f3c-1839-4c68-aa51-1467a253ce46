package com.cottontang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Homepage;
import com.cottontang.mapper.HomepageMapper;
import com.cottontang.service.HomepageService;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;

/**
 * 首页数据服务实现
 * 
 * <AUTHOR>
 */
@Service
public class HomepageServiceImpl extends ServiceImpl<HomepageMapper, Homepage> implements HomepageService {
    
    @Override
    public Page<Homepage> getHomepageList(PageQuery pageQuery) {
        Page<Homepage> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        
        LambdaQueryWrapper<Homepage> wrapper = new LambdaQueryWrapper<>();
        
        // 搜索条件
        if (StrUtil.isNotBlank(pageQuery.getKeyword())) {
            wrapper.like(Homepage::getPihaoKunkao, pageQuery.getKeyword())
                   .or()
                   .like(Homepage::getLeixing, pageQuery.getKeyword())
                   .or()
                   .like(Homepage::getJiagongchang, pageQuery.getKeyword())
                   .or()
                   .like(Homepage::getCangchumingcheng, pageQuery.getKeyword());
        }
        
        // 排序
        wrapper.orderByDesc(Homepage::getOrderId)
               .orderByDesc(Homepage::getId);
        
        return this.page(page, wrapper);
    }
    
    @Override
    public boolean addHomepage(Homepage homepage) {
        homepage.setAddTime(System.currentTimeMillis() / 1000);
        if (homepage.getOrderId() == null) {
            homepage.setOrderId(100);
        }
        return this.save(homepage);
    }
    
    @Override
    public boolean updateHomepage(Homepage homepage) {
        return this.updateById(homepage);
    }
    
    @Override
    public boolean deleteHomepage(Long id) {
        return this.removeById(id);
    }
    
    @Override
    public Page<Homepage> getHomepageByType(String danType, PageQuery pageQuery) {
        Page<Homepage> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        
        LambdaQueryWrapper<Homepage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Homepage::getDanType, danType);
        
        // 搜索条件
        if (StrUtil.isNotBlank(pageQuery.getKeyword())) {
            wrapper.and(w -> w.like(Homepage::getPihaoKunkao, pageQuery.getKeyword())
                           .or()
                           .like(Homepage::getLeixing, pageQuery.getKeyword())
                           .or()
                           .like(Homepage::getJiagongchang, pageQuery.getKeyword())
                           .or()
                           .like(Homepage::getCangchumingcheng, pageQuery.getKeyword()));
        }
        
        // 排序
        wrapper.orderByDesc(Homepage::getOrderId)
               .orderByDesc(Homepage::getId);
        
        return this.page(page, wrapper);
    }
}
