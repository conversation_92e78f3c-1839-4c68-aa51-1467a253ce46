package com.cottontang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.File;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * File service interface
 * 
 * <AUTHOR>
 */
public interface FileService extends IService<File> {
    
    /**
     * Upload file
     * 
     * @param file file to upload
     * @param uploaderId uploader ID
     * @param uploaderType uploader type (1: admin, 2: user)
     * @return uploaded file info
     */
    Map<String, Object> uploadFile(MultipartFile file, Long uploaderId, Integer uploaderType);
    
    /**
     * Get file list with pagination
     * 
     * @param pageQuery pagination and search parameters
     * @return file page
     */
    Page<File> getFileList(PageQuery pageQuery);
    
    /**
     * Get file by ID
     * 
     * @param id file ID
     * @return file info
     */
    File getFileById(Long id);
    
    /**
     * Delete file
     * 
     * @param id file ID
     * @return true if successful
     */
    boolean deleteFile(Long id);
    
    /**
     * Batch delete files
     * 
     * @param ids file IDs
     * @return true if successful
     */
    boolean batchDeleteFiles(List<Long> ids);
} 