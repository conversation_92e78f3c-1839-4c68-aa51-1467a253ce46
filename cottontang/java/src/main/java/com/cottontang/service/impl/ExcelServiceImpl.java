package com.cottontang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cottontang.dto.excel.CustomOrderExcelDTO;
import com.cottontang.dto.excel.HomepageExcelDTO;
import com.cottontang.entity.CustomOrder;
import com.cottontang.entity.ExcelFile;
import com.cottontang.entity.Homepage;
import com.cottontang.entity.User;
import com.cottontang.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExcelServiceImpl implements ExcelService {
    
    private final HomepageService homepageService;
    private final CustomOrderService customOrderService;
    private final UserService userService;
    private final ExcelFileService excelFileService;
    
    @Value("${file.upload.path:upload/}")
    private String uploadPath;
    
    @Override
    public ExcelFile importHomepageData(MultipartFile file, Long adminUid) {
        // 创建Excel文件记录
        ExcelFile excelFile = new ExcelFile();
        excelFile.setTitle("首页数据导入");
        excelFile.setAddTime(System.currentTimeMillis() / 1000);
        excelFile.setAdminUid(adminUid);
        excelFile.setFileSize(file.getSize());
        excelFile.setStatus(1); // 处理中
        
        try {
            // 保存文件
            String fileName = saveUploadFile(file);
            excelFile.setFilePath(fileName);
            
            // 保存记录
            excelFileService.save(excelFile);
            
            // 读取Excel数据
            List<HomepageExcelDTO> dataList = new ArrayList<>();
            EasyExcel.read(file.getInputStream(), HomepageExcelDTO.class, new ReadListener<HomepageExcelDTO>() {
                @Override
                public void invoke(HomepageExcelDTO data, AnalysisContext context) {
                    dataList.add(data);
                }
                
                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel读取完成，共{}条数据", dataList.size());
                }
            }).sheet().doRead();
            
            // 转换并保存数据
            int successCount = 0;
            for (HomepageExcelDTO dto : dataList) {
                try {
                    Homepage homepage = new Homepage();
                    BeanUtil.copyProperties(dto, homepage);
                    homepage.setAddTime(System.currentTimeMillis() / 1000);
                    homepage.setOrderId(100);
                    
                    if (homepageService.save(homepage)) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("保存首页数据失败: {}", e.getMessage());
                }
            }
            
            // 更新Excel文件记录
            excelFile.setStatus(2); // 已完成
            excelFile.setRecordCount(successCount);
            excelFileService.updateById(excelFile);
            
        } catch (Exception e) {
            log.error("导入Excel失败: {}", e.getMessage());
            excelFile.setStatus(3); // 失败
            excelFile.setErrorMessage(e.getMessage());
            excelFileService.updateById(excelFile);
        }
        
        return excelFile;
    }
    
    @Override
    public void exportHomepageData(HttpServletResponse response, String danType) {
        try {
            // 设置响应头
            String fileName = "首页数据_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            
            // 查询数据
            LambdaQueryWrapper<Homepage> wrapper = new LambdaQueryWrapper<>();
            if (StrUtil.isNotBlank(danType)) {
                wrapper.eq(Homepage::getDanType, danType);
            }
            wrapper.orderByDesc(Homepage::getOrderId).orderByDesc(Homepage::getId);
            
            List<Homepage> homepageList = homepageService.list(wrapper);
            
            // 转换为Excel DTO
            List<HomepageExcelDTO> excelData = new ArrayList<>();
            for (Homepage homepage : homepageList) {
                HomepageExcelDTO dto = new HomepageExcelDTO();
                BeanUtil.copyProperties(homepage, dto);
                excelData.add(dto);
            }
            
            // 写入Excel
            EasyExcel.write(response.getOutputStream(), HomepageExcelDTO.class)
                    .sheet("首页数据")
                    .doWrite(excelData);
                    
        } catch (IOException e) {
            log.error("导出Excel失败: {}", e.getMessage());
        }
    }

    @Override
    public ExcelFile importCustomOrderData(MultipartFile file, Long adminUid) {
        // 创建Excel文件记录
        ExcelFile excelFile = new ExcelFile();
        excelFile.setTitle("定制订单导入");
        excelFile.setAddTime(System.currentTimeMillis() / 1000);
        excelFile.setAdminUid(adminUid);
        excelFile.setFileSize(file.getSize());
        excelFile.setStatus(1); // 处理中

        try {
            // 保存文件
            String fileName = saveUploadFile(file);
            excelFile.setFilePath(fileName);

            // 保存记录
            excelFileService.save(excelFile);

            // 读取Excel数据
            List<CustomOrderExcelDTO> dataList = new ArrayList<>();
            EasyExcel.read(file.getInputStream(), CustomOrderExcelDTO.class, new ReadListener<CustomOrderExcelDTO>() {
                @Override
                public void invoke(CustomOrderExcelDTO data, AnalysisContext context) {
                    dataList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel读取完成，共{}条数据", dataList.size());
                }
            }).sheet().doRead();

            // 转换并保存数据
            int successCount = 0;
            for (CustomOrderExcelDTO dto : dataList) {
                try {
                    CustomOrder customOrder = new CustomOrder();
                    BeanUtil.copyProperties(dto, customOrder);
                    customOrder.setAddTime(System.currentTimeMillis() / 1000);
                    customOrder.setStatus(0); // 默认待处理

                    if (customOrderService.save(customOrder)) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("保存定制订单失败: {}", e.getMessage());
                }
            }

            // 更新Excel文件记录
            excelFile.setStatus(2); // 已完成
            excelFile.setRecordCount(successCount);
            excelFileService.updateById(excelFile);

        } catch (Exception e) {
            log.error("导入Excel失败: {}", e.getMessage());
            excelFile.setStatus(3); // 失败
            excelFile.setErrorMessage(e.getMessage());
            excelFileService.updateById(excelFile);
        }

        return excelFile;
    }

    @Override
    public void exportCustomOrderData(HttpServletResponse response, Integer status) {
        try {
            // 设置响应头
            String fileName = "定制订单_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

            // 查询数据
            LambdaQueryWrapper<CustomOrder> wrapper = new LambdaQueryWrapper<>();
            if (status != null) {
                wrapper.eq(CustomOrder::getStatus, status);
            }
            wrapper.orderByDesc(CustomOrder::getAddTime);

            List<CustomOrder> orderList = customOrderService.list(wrapper);

            // 转换为Excel DTO
            List<CustomOrderExcelDTO> excelData = new ArrayList<>();
            for (CustomOrder order : orderList) {
                CustomOrderExcelDTO dto = new CustomOrderExcelDTO();
                BeanUtil.copyProperties(order, dto);
                // 转换状态和时间
                dto.setStatus(getStatusText(order.getStatus()));
                dto.setAddTime(formatTime(order.getAddTime()));
                dto.setUid(order.getUid() != null ? order.getUid().toString() : "");
                excelData.add(dto);
            }

            // 写入Excel
            EasyExcel.write(response.getOutputStream(), CustomOrderExcelDTO.class)
                    .sheet("定制订单")
                    .doWrite(excelData);

        } catch (IOException e) {
            log.error("导出Excel失败: {}", e.getMessage());
        }
    }

    @Override
    public ExcelFile importUserData(MultipartFile file, Long adminUid) {
        // 用户数据导入实现（简化版）
        ExcelFile excelFile = new ExcelFile();
        excelFile.setTitle("用户数据导入");
        excelFile.setAddTime(System.currentTimeMillis() / 1000);
        excelFile.setAdminUid(adminUid);
        excelFile.setFileSize(file.getSize());
        excelFile.setStatus(2); // 已完成（简化实现）
        excelFile.setRecordCount(0);

        try {
            String fileName = saveUploadFile(file);
            excelFile.setFilePath(fileName);
            excelFileService.save(excelFile);
        } catch (Exception e) {
            log.error("导入用户数据失败: {}", e.getMessage());
            excelFile.setStatus(3);
            excelFile.setErrorMessage(e.getMessage());
        }

        return excelFile;
    }

    @Override
    public void exportUserData(HttpServletResponse response) {
        try {
            // 设置响应头
            String fileName = "用户数据_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

            // 查询用户数据
            List<User> userList = userService.list();

            // 简化的用户数据导出（可以创建UserExcelDTO来完善）
            List<List<String>> data = new ArrayList<>();
            data.add(List.of("用户名", "昵称", "邮箱", "公司", "状态", "注册时间"));

            for (User user : userList) {
                data.add(List.of(
                    user.getUsername() != null ? user.getUsername() : "",
                    user.getNickname() != null ? user.getNickname() : "",
                    user.getEmail() != null ? user.getEmail() : "",
                    user.getCompany() != null ? user.getCompany() : "",
                    user.getStatus() != null ? (user.getStatus() == 1 ? "启用" : "禁用") : "",
                    formatTime(user.getAddTime())
                ));
            }

            // 写入Excel
            EasyExcel.write(response.getOutputStream())
                    .sheet("用户数据")
                    .doWrite(data);

        } catch (IOException e) {
            log.error("导出用户数据失败: {}", e.getMessage());
        }
    }

    @Override
    public List<ExcelFile> getExcelFileList() {
        return excelFileService.list(new LambdaQueryWrapper<ExcelFile>()
                .orderByDesc(ExcelFile::getAddTime));
    }

    @Override
    public ExcelFile getExcelFileById(Long id) {
        return excelFileService.getById(id);
    }

    @Override
    public boolean deleteExcelFile(Long id) {
        ExcelFile excelFile = excelFileService.getById(id);
        if (excelFile != null && StrUtil.isNotBlank(excelFile.getFilePath())) {
            // 删除物理文件
            try {
                File file = new File(uploadPath + excelFile.getFilePath());
                if (file.exists()) {
                    file.delete();
                }
            } catch (Exception e) {
                log.error("删除文件失败: {}", e.getMessage());
            }
        }
        return excelFileService.removeById(id);
    }

    /**
     * 保存上传文件
     */
    private String saveUploadFile(MultipartFile file) throws IOException {
        String originalFilename = file.getOriginalFilename();
        String extension = FileUtil.extName(originalFilename);
        String fileName = System.currentTimeMillis() + "." + extension;

        File uploadDir = new File(uploadPath);
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }

        File destFile = new File(uploadDir, fileName);
        file.transferTo(destFile);

        return fileName;
    }

    /**
     * 格式化时间
     */
    private String formatTime(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        return LocalDateTime.ofEpochSecond(timestamp, 0, java.time.ZoneOffset.ofHours(8))
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 获取状态文本
     */
    private String getStatusText(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0: return "待处理";
            case 1: return "已处理";
            case 2: return "已取消";
            default: return "未知";
        }
    }
}
