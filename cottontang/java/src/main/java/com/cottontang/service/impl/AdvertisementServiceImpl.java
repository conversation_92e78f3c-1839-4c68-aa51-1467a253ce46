package com.cottontang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.dto.AdvertisementDTO;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Advertisement;
import com.cottontang.exception.BusinessException;
import com.cottontang.mapper.AdvertisementMapper;
import com.cottontang.service.AdvertisementService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Advertisement service implementation
 *
 * <AUTHOR>
 */
@Service
public class AdvertisementServiceImpl extends ServiceImpl<AdvertisementMapper, Advertisement> implements AdvertisementService {

    /**
     * Get advertisement list with pagination
     *
     * @param pageQuery pagination and search parameters
     * @return advertisement page
     */
    @Override
    public Page<Advertisement> getAdvertisementList(PageQuery pageQuery) {
        // Create page
        Page<Advertisement> page = new Page<>(pageQuery.getCurrent(), pageQuery.getSize());

        // Create query wrapper
        LambdaQueryWrapper<Advertisement> wrapper = new LambdaQueryWrapper<>();

        // Add search condition if keyword is provided
        if (StrUtil.isNotBlank(pageQuery.getKeyword())) {
            wrapper.like(Advertisement::getTitle, pageQuery.getKeyword());
        }

        // Add sort conditions
        wrapper.orderByAsc(Advertisement::getOrderId).orderByDesc(Advertisement::getId);

        // Execute query
        return page(page, wrapper);
    }

    /**
     * Get advertisements by type
     *
     * @param type advertisement type
     * @return advertisement list
     */
    @Override
    public List<Advertisement> getAdvertisementsByType(Integer type) {
        return list(new LambdaQueryWrapper<Advertisement>()
                .eq(Advertisement::getType, type)
                .orderByAsc(Advertisement::getOrderId)
                .orderByDesc(Advertisement::getId));
    }

    /**
     * Add advertisement
     *
     * @param advertisementDTO advertisement data
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addAdvertisement(AdvertisementDTO advertisementDTO) {
        // Create new advertisement
        Advertisement advertisement = new Advertisement();
        BeanUtil.copyProperties(advertisementDTO, advertisement);

        // Set default values
        if (advertisement.getOrderId() == null) {
            advertisement.setOrderId(100);
        }

        return save(advertisement);
    }

    /**
     * Update advertisement
     *
     * @param advertisementDTO advertisement data
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAdvertisement(AdvertisementDTO advertisementDTO) {
        // Check if advertisement exists
        Advertisement advertisement = getById(advertisementDTO.getId());
        if (advertisement == null) {
            throw new BusinessException("Advertisement not found");
        }

        // Update advertisement properties
        BeanUtil.copyProperties(advertisementDTO, advertisement);

        return updateById(advertisement);
    }

    /**
     * Delete advertisement
     *
     * @param id advertisement ID
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAdvertisement(Long id) {
        // Check if advertisement exists
        Advertisement advertisement = getById(id);
        if (advertisement == null) {
            throw new BusinessException("Advertisement not found");
        }

        // Delete advertisement
        return removeById(id);
    }

    /**
     * Update advertisement order ID
     *
     * @param id advertisement ID
     * @param orderId new order ID
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderId(Long id, Integer orderId) {
        // Check if advertisement exists
        Advertisement advertisement = getById(id);
        if (advertisement == null) {
            throw new BusinessException("Advertisement not found");
        }

        // Update order ID
        advertisement.setOrderId(orderId);
        return updateById(advertisement);
    }
}
