package com.cottontang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.entity.Article;
import com.cottontang.entity.Category;
import com.cottontang.exception.BusinessException;
import com.cottontang.mapper.ArticleMapper;
import com.cottontang.mapper.CategoryMapper;
import com.cottontang.service.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分类服务实现
 * 
 * <AUTHOR>
 */
@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements CategoryService {
    
    @Autowired
    private ArticleMapper articleMapper;
    
    /**
     * 根据父ID获取分类列表
     * 
     * @param pid 父ID（0表示顶层）
     * @return 分类列表
     */
    @Override
    public List<Category> getCategoriesByParentId(Long pid) {
        return list(new LambdaQueryWrapper<Category>()
                .eq(Category::getPid, pid)
                .orderByDesc(Category::getOrderId, Category::getId));
    }
    
    /**
     * 获取分类树（层级结构）
     * 
     * @return 分类树
     */
    @Override
    public List<Category> getCategoryTree() {
        // 获取所有分类
        List<Category> allCategories = list(new LambdaQueryWrapper<Category>()
                .orderByDesc(Category::getOrderId)
                .orderByAsc(Category::getId));
                
        // 按父ID分组分类
        Map<Long, List<Category>> categoriesByParentId = allCategories.stream()
                .collect(Collectors.groupingBy(Category::getPid));
                
        // 为每个父分类设置子分类
        allCategories.forEach(category -> 
            category.setChildren(categoriesByParentId.getOrDefault(category.getId(), null)));
        
        // 只返回顶层分类
        return allCategories.stream()
                .filter(category -> category.getPid() == 0)
                .collect(Collectors.toList());
    }
    
    /**
     * 添加分类
     * 
     * @param category 分类数据
     * @return 如果成功则为true
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addCategory(Category category) {
        // 验证父分类是否存在（如果不是顶层分类）
        if (category.getPid() != 0) {
            Category parent = getById(category.getPid());
            if (parent == null) {
                throw new BusinessException("Parent category not found");
            }
        }
        
        // 如果未提供排序ID，则设置默认值
        if (category.getOrderId() == null) {
            category.setOrderId(100);
        }
        
        return save(category);
    }
    
    /**
     * 更新分类
     * 
     * @param category 分类数据
     * @return 如果成功则为true
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCategory(Category category) {
        // 检查分类是否存在
        Category existing = getById(category.getId());
        if (existing == null) {
            throw new BusinessException("Category not found");
        }
        
        // 验证父分类是否存在（如果不是顶层分类）
        if (category.getPid() != 0) {
            Category parent = getById(category.getPid());
            if (parent == null) {
                throw new BusinessException("Parent category not found");
            }
            
            // 防止循环引用
            if (category.getId().equals(category.getPid())) {
                throw new BusinessException("Category cannot be its own parent");
            }
        }
        
        return updateById(category);
    }
    
    /**
     * 删除分类
     * 
     * @param id 分类ID
     * @return 如果成功则为true
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCategory(Long id) {
        // 检查分类是否存在
        Category category = getById(id);
        if (category == null) {
            throw new BusinessException("Category not found");
        }
        
        // 检查分类是否有子分类
        int childCount = count(new LambdaQueryWrapper<Category>()
                .eq(Category::getPid, id));
        if (childCount > 0) {
            throw new BusinessException("Please delete child categories first");
        }
        
        // 检查分类是否有文章
        int articleCount = articleMapper.selectCount(new LambdaQueryWrapper<Article>()
                .eq(Article::getCatId, id));
        if (articleCount > 0) {
            throw new BusinessException("Please delete articles in this category first");
        }
        
        return removeById(id);
    }
} 