package com.cottontang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.entity.SystemSetting;
import com.cottontang.mapper.SystemSettingMapper;
import com.cottontang.service.SystemSettingService;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统设置服务实现
 * 
 * <AUTHOR>
 */
@Service
public class SystemSettingServiceImpl extends ServiceImpl<SystemSettingMapper, SystemSetting> implements SystemSettingService {
    
    @Override
    public String getSettingValue(String key) {
        return getSettingValue(key, null);
    }
    
    @Override
    public String getSettingValue(String key, String defaultValue) {
        if (StrUtil.isBlank(key)) {
            return defaultValue;
        }
        
        LambdaQueryWrapper<SystemSetting> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SystemSetting::getKey, key);
        
        SystemSetting setting = this.getOne(wrapper);
        return setting != null ? setting.getValue() : defaultValue;
    }
    
    @Override
    public boolean setSetting(String key, String value) {
        if (StrUtil.isBlank(key)) {
            return false;
        }
        
        LambdaQueryWrapper<SystemSetting> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SystemSetting::getKey, key);
        
        SystemSetting existingSetting = this.getOne(wrapper);
        
        if (existingSetting != null) {
            // 更新现有设置
            existingSetting.setValue(value);
            return this.updateById(existingSetting);
        } else {
            // 创建新设置
            SystemSetting newSetting = new SystemSetting();
            newSetting.setKey(key);
            newSetting.setValue(value);
            return this.save(newSetting);
        }
    }
    
    @Override
    public boolean setSettings(Map<String, String> settings) {
        if (settings == null || settings.isEmpty()) {
            return false;
        }
        
        boolean allSuccess = true;
        for (Map.Entry<String, String> entry : settings.entrySet()) {
            if (!setSetting(entry.getKey(), entry.getValue())) {
                allSuccess = false;
            }
        }
        
        return allSuccess;
    }
    
    @Override
    public List<SystemSetting> getSettingsByGroup(String group) {
        LambdaQueryWrapper<SystemSetting> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SystemSetting::getGroup, group);
        wrapper.orderByAsc(SystemSetting::getSort);
        
        return this.list(wrapper);
    }
    
    @Override
    public Map<String, String> getAllSettings() {
        List<SystemSetting> settings = this.list();
        Map<String, String> settingMap = new HashMap<>();
        
        for (SystemSetting setting : settings) {
            settingMap.put(setting.getKey(), setting.getValue());
        }
        
        return settingMap;
    }
    
    @Override
    public boolean deleteSetting(String key) {
        if (StrUtil.isBlank(key)) {
            return false;
        }
        
        LambdaQueryWrapper<SystemSetting> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SystemSetting::getKey, key);
        
        return this.remove(wrapper);
    }
}
