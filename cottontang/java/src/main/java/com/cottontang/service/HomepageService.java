package com.cottontang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Homepage;

/**
 * 首页数据服务接口
 * 
 * <AUTHOR>
 */
public interface HomepageService extends IService<Homepage> {
    
    /**
     * 获取首页数据列表（分页）
     * 
     * @param pageQuery 分页和搜索参数
     * @return 首页数据分页
     */
    Page<Homepage> getHomepageList(PageQuery pageQuery);
    
    /**
     * 添加首页数据
     * 
     * @param homepage 首页数据
     * @return 如果成功则为true
     */
    boolean addHomepage(Homepage homepage);
    
    /**
     * 更新首页数据
     * 
     * @param homepage 首页数据
     * @return 如果成功则为true
     */
    boolean updateHomepage(Homepage homepage);
    
    /**
     * 根据ID删除首页数据
     * 
     * @param id 首页数据ID
     * @return 如果成功则为true
     */
    boolean deleteHomepage(Long id);
    
    /**
     * 根据类型获取首页数据
     * 
     * @param danType 单据类型
     * @param pageQuery 分页参数
     * @return 首页数据分页
     */
    Page<Homepage> getHomepageByType(String danType, PageQuery pageQuery);
}
