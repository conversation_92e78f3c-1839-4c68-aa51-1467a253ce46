package com.cottontang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.common.PageQuery;
import com.cottontang.entity.CustomOrder;
import com.cottontang.mapper.CustomOrderMapper;
import com.cottontang.service.CustomOrderService;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;

/**
 * 定制订单服务实现
 * 
 * <AUTHOR>
 */
@Service
public class CustomOrderServiceImpl extends ServiceImpl<CustomOrderMapper, CustomOrder> implements CustomOrderService {
    
    @Override
    public Page<CustomOrder> getCustomOrderList(PageQuery pageQuery) {
        Page<CustomOrder> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        
        LambdaQueryWrapper<CustomOrder> wrapper = new LambdaQueryWrapper<>();
        
        // 搜索条件
        if (StrUtil.isNotBlank(pageQuery.getKeyword())) {
            wrapper.like(CustomOrder::getDingzhiNo, pageQuery.getKeyword())
                   .or()
                   .like(CustomOrder::getPihaoKunkao, pageQuery.getKeyword())
                   .or()
                   .like(CustomOrder::getLeixing, pageQuery.getKeyword());
        }
        
        // 排序
        wrapper.orderByDesc(CustomOrder::getAddTime);
        
        return this.page(page, wrapper);
    }
    
    @Override
    public boolean addCustomOrder(CustomOrder customOrder) {
        customOrder.setAddTime(System.currentTimeMillis() / 1000);
        customOrder.setStatus(0); // 默认待处理状态
        
        // 生成订制编号
        if (StrUtil.isBlank(customOrder.getDingzhiNo())) {
            customOrder.setDingzhiNo("D" + System.currentTimeMillis());
        }
        
        return this.save(customOrder);
    }
    
    @Override
    public boolean updateCustomOrder(CustomOrder customOrder) {
        return this.updateById(customOrder);
    }
    
    @Override
    public boolean deleteCustomOrder(Long id) {
        return this.removeById(id);
    }
    
    @Override
    public Page<CustomOrder> getCustomOrdersByUserId(Long uid, PageQuery pageQuery) {
        Page<CustomOrder> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        
        LambdaQueryWrapper<CustomOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomOrder::getUid, uid);
        
        // 搜索条件
        if (StrUtil.isNotBlank(pageQuery.getKeyword())) {
            wrapper.and(w -> w.like(CustomOrder::getDingzhiNo, pageQuery.getKeyword())
                           .or()
                           .like(CustomOrder::getPihaoKunkao, pageQuery.getKeyword())
                           .or()
                           .like(CustomOrder::getLeixing, pageQuery.getKeyword()));
        }
        
        // 排序
        wrapper.orderByDesc(CustomOrder::getAddTime);
        
        return this.page(page, wrapper);
    }
    
    @Override
    public boolean updateOrderStatus(Long id, Integer status) {
        CustomOrder customOrder = new CustomOrder();
        customOrder.setId(id);
        customOrder.setStatus(status);
        return this.updateById(customOrder);
    }
}
