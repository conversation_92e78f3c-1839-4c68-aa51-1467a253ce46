package com.cottontang.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.dto.AdminDTO;
import com.cottontang.dto.LoginDTO;
import com.cottontang.dto.PageQuery;
import com.cottontang.dto.UserInfoDTO;
import com.cottontang.entity.Admin;
import com.cottontang.exception.BusinessException;
import com.cottontang.mapper.AdminMapper;
import com.cottontang.service.AdminService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * Admin service implementation
 * 
 * <AUTHOR>
 */
@Service
public class AdminServiceImpl extends ServiceImpl<AdminMapper, Admin> implements AdminService {
    
    /**
     * Admin login
     * 
     * @param loginDTO login parameters
     * @return token string
     */
    @Override
    public String login(LoginDTO loginDTO) {
        // 根据用户名查找管理员
        Admin admin = getOne(new LambdaQueryWrapper<Admin>()
                .eq(Admin::getName, loginDTO.getUsername()));
                
        // 验证管理员是否存在
        if (admin == null) {
            throw new BusinessException("User not found");
        }
        
        // 验证密码
        if (!SaSecureUtil.md5(loginDTO.getPassword()).equals(admin.getPwd())) {
            throw new BusinessException("Invalid username or password");
        }
        
        // 检查管理员状态
        if (admin.getStatus() != null && admin.getStatus() != 1) {
            throw new BusinessException("User is disabled");
        }
        
        // 设置登录ID（会话ID）
        StpUtil.login(admin.getId());
        
        // 更新最后登录时间
        // 在原PHP代码中，没有直接更新登录时间
        
        // 返回令牌
        return StpUtil.getTokenValue();
    }
    
    /**
     * Get admin info by ID
     * 
     * @param adminId admin ID
     * @return admin user info
     */
    @Override
    public UserInfoDTO getAdminInfo(Long adminId) {
        // 根据ID查找管理员
        Admin admin = getById(adminId);
        
        if (admin == null) {
            throw new BusinessException("User not found");
        }
        
        // 转换为UserInfoDTO
        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setId(admin.getId());
        userInfo.setUsername(admin.getName());
        userInfo.setRealName(admin.getRealName());
        userInfo.setType(admin.getType());
        
        // 根据管理员类型设置角色
        if (admin.getType() != null && admin.getType() == 0) {
            userInfo.setRoles(Arrays.asList("admin", "super_admin"));
            // 在实际应用中，权限将从数据库加载
            userInfo.setPermissions(Arrays.asList("*:*:*"));
        } else {
            userInfo.setRoles(Arrays.asList("admin"));
            userInfo.setPermissions(new ArrayList<>());
        }
        
        return userInfo;
    }
    
    /**
     * 登出当前管理员
     */
    @Override
    public void logout() {
        StpUtil.logout();
    }
    
    /**
     * 获取管理员列表（分页）
     * 
     * @param pageQuery 分页和搜索参数
     * @return 管理员分页
     */
    @Override
    public Page<Admin> getAdminList(PageQuery pageQuery) {
        // 创建分页
        Page<Admin> page = new Page<>(pageQuery.getCurrent(), pageQuery.getSize());
        
        // 创建查询包装器
        LambdaQueryWrapper<Admin> wrapper = new LambdaQueryWrapper<>();
        
        // 如果提供了关键词，添加搜索条件
        if (StrUtil.isNotBlank(pageQuery.getKeyword())) {
            wrapper.like(Admin::getName, pageQuery.getKeyword())
                   .or()
                   .like(Admin::getRealName, pageQuery.getKeyword());
        }
        
        // 添加排序条件
        wrapper.orderByAsc(Admin::getOrderId).orderByDesc(Admin::getId);
        
        // 执行查询
        return page(page, wrapper);
    }
    
    /**
     * 添加管理员用户
     * 
     * @param adminDTO 管理员数据
     * @return 如果成功则为true
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addAdmin(AdminDTO adminDTO) {
        // 检查用户名是否已存在
        long count = count(new LambdaQueryWrapper<Admin>()
                .eq(Admin::getName, adminDTO.getName()));
                
        if (count > 0) {
            throw new BusinessException("Username already exists");
        }
        
        // 创建新管理员
        Admin admin = new Admin();
        BeanUtil.copyProperties(adminDTO, admin);
        
        // 设置默认值
        if (admin.getStatus() == null) {
            admin.setStatus(1);
        }
        
        if (admin.getType() == null) {
            admin.setType(1); // 默认为普通管理员
        }
        
        if (admin.getOrderId() == null) {
            admin.setOrderId(100);
        }
        
        // 加密密码
        if (StrUtil.isNotBlank(adminDTO.getPassword())) {
            admin.setPwd(SaSecureUtil.md5(adminDTO.getPassword()));
        } else {
            // 设置默认密码
            admin.setPwd(SaSecureUtil.md5("123456"));
        }
        
        // 保存管理员
        return save(admin);
    }
    
    /**
     * 更新管理员用户
     * 
     * @param adminDTO 管理员数据
     * @return 如果成功则为true
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAdmin(AdminDTO adminDTO) {
        // 检查管理员是否存在
        Admin admin = getById(adminDTO.getId());
        if (admin == null) {
            throw new BusinessException("Admin user not found");
        }
        
        // 检查用户名是否已更改并且已存在
        if (!admin.getName().equals(adminDTO.getName())) {
            long count = count(new LambdaQueryWrapper<Admin>()
                    .eq(Admin::getName, adminDTO.getName()));
                    
            if (count > 0) {
                throw new BusinessException("Username already exists");
            }
        }
        
        // 更新管理员属性
        BeanUtil.copyProperties(adminDTO, admin);
        
        // 如果提供了密码，则更新密码
        if (StrUtil.isNotBlank(adminDTO.getPassword())) {
            admin.setPwd(SaSecureUtil.md5(adminDTO.getPassword()));
        }
        
        // 更新管理员
        return updateById(admin);
    }
    
    /**
     * 根据ID删除管理员用户
     * 
     * @param id 管理员ID
     * @return 如果成功则为true
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAdmin(Long id) {
        // 检查管理员是否存在
        Admin admin = getById(id);
        if (admin == null) {
            throw new BusinessException("Admin user not found");
        }
        
        // 防止删除超级管理员
        if (admin.getType() != null && admin.getType() == 0) {
            throw new BusinessException("Cannot delete super admin");
        }
        
        // 防止删除当前用户
        if (ObjectUtil.equal(id, StpUtil.getLoginIdAsLong())) {
            throw new BusinessException("Cannot delete current user");
        }
        
        // 删除管理员
        return removeById(id);
    }
    
    /**
     * 更改管理员状态
     * 
     * @param id 管理员ID
     * @param status 新状态 (0: 禁用, 1: 启用)
     * @return 如果成功则为true
     */
    @Override
    public boolean changeStatus(Long id, Integer status) {
        // 检查管理员是否存在
        Admin admin = getById(id);
        if (admin == null) {
            throw new BusinessException("Admin user not found");
        }
        
        // 防止禁用超级管理员
        if (admin.getType() != null && admin.getType() == 0 && status == 0) {
            throw new BusinessException("Cannot disable super admin");
        }
        
        // 更新状态
        admin.setStatus(status);
        return updateById(admin);
    }
    
    /**
     * 更新管理员排序ID
     * 
     * @param id 管理员ID
     * @param orderId 新排序ID
     * @return 如果成功则为true
     */
    @Override
    public boolean updateOrderId(Long id, Integer orderId) {
        // 检查管理员是否存在
        Admin admin = getById(id);
        if (admin == null) {
            throw new BusinessException("Admin user not found");
        }
        
        // 更新排序ID
        admin.setOrderId(orderId);
        return updateById(admin);
    }
} 