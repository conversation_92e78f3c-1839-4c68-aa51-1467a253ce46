package com.cottontang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Store;
import com.cottontang.entity.Warehouse;
import com.cottontang.exception.BusinessException;
import com.cottontang.mapper.StoreMapper;
import com.cottontang.mapper.WarehouseMapper;
import com.cottontang.service.StoreService;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Store service implementation
 * 
 * <AUTHOR>
 */
@Service
public class StoreServiceImpl extends ServiceImpl<StoreMapper, Store> implements StoreService {
    
    @Autowired
    private WarehouseMapper warehouseMapper;
    
    /**
     * Get store list with pagination and filtering
     * 
     * @param pageQuery pagination and search parameters
     * @return store page
     */
    @Override
    public Page<Store> getStoreList(PageQuery pageQuery) {
        Page<Store> page = new Page<>(pageQuery.getPage(), pageQuery.getLimit());
        
        LambdaQueryWrapper<Store> queryWrapper = new LambdaQueryWrapper<>();
        Map<String, Object> params = pageQuery.getParams();
        
        // Filter by batch/kunk number
        if (params.containsKey("pihaoKunkao") && StringUtils.hasText(params.get("pihaoKunkao").toString())) {
            queryWrapper.eq(Store::getPihaoKunkao, params.get("pihaoKunkao"));
        }
        
        // Filter by store type
        if (params.containsKey("leixing") && StringUtils.hasText(params.get("leixing").toString())) {
            queryWrapper.eq(Store::getLeixing, params.get("leixing"));
        }
        
        // Filter by annual period
        if (params.containsKey("niandu") && StringUtils.hasText(params.get("niandu").toString())) {
            queryWrapper.eq(Store::getNiandu, params.get("niandu"));
        }
        
        // Filter by color grade and quality
        if (params.containsKey("yansejiPinji") && StringUtils.hasText(params.get("yansejiPinji").toString())) {
            queryWrapper.eq(Store::getYansejiPinji, params.get("yansejiPinji"));
        }
        
        // Filter by status
        if (params.containsKey("zhuangtai") && StringUtils.hasText(params.get("zhuangtai").toString())) {
            queryWrapper.eq(Store::getZhuangtai, params.get("zhuangtai"));
        }
        
        // Filter by warehouse ID
        if (params.containsKey("cangkuId") && params.get("cangkuId") != null) {
            queryWrapper.eq(Store::getCangkuId, params.get("cangkuId"));
        }
        
        // Filter by search keyword in title
        if (params.containsKey("keyword") && StringUtils.hasText(params.get("keyword").toString())) {
            queryWrapper.like(Store::getTitle, params.get("keyword"));
        }
        
        // Check if current user is admin
        if (StpUtil.hasRole("user")) {
            // If user is not admin, only show their own stores
            queryWrapper.eq(Store::getUid, StpUtil.getLoginIdAsLong());
        }
        
        // Sort by timestamp descending
        queryWrapper.orderByDesc(Store::getAddTime);
        
        // Execute query
        Page<Store> result = page(page, queryWrapper);
        
        // Populate transient fields
        for (Store store : result.getRecords()) {
            if (store.getCangkuId() != null) {
                Warehouse warehouse = warehouseMapper.selectById(store.getCangkuId());
                if (warehouse != null) {
                    store.setCangkuName(warehouse.getName());
                }
            }
        }
        
        return result;
    }
    
    /**
     * Add store
     * 
     * @param store store data
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addStore(Store store) {
        // Set current user ID as creator
        store.setUid(StpUtil.getLoginIdAsLong());
        
        // Set current timestamp
        store.setAddTime(System.currentTimeMillis() / 1000);
        
        // Check if warehouse exists
        if (store.getCangkuId() != null) {
            Warehouse warehouse = warehouseMapper.selectById(store.getCangkuId());
            if (warehouse == null) {
                throw new BusinessException("Warehouse not found");
            }
        }
        
        return save(store);
    }
    
    /**
     * Update store
     * 
     * @param store store data
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStore(Store store) {
        // Check if store exists
        Store existing = getById(store.getId());
        if (existing == null) {
            throw new BusinessException("Store not found");
        }
        
        // Check if user has permission
        if (!StpUtil.hasRole("admin") && !existing.getUid().equals(StpUtil.getLoginIdAsLong())) {
            throw new BusinessException("Permission denied");
        }
        
        // Check if warehouse exists
        if (store.getCangkuId() != null) {
            Warehouse warehouse = warehouseMapper.selectById(store.getCangkuId());
            if (warehouse == null) {
                throw new BusinessException("Warehouse not found");
            }
        }
        
        return updateById(store);
    }
    
    /**
     * Delete store
     * 
     * @param id store ID
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteStore(Long id) {
        // Check if store exists
        Store store = getById(id);
        if (store == null) {
            throw new BusinessException("Store not found");
        }
        
        // Check if user has permission
        if (!StpUtil.hasRole("admin") && !store.getUid().equals(StpUtil.getLoginIdAsLong())) {
            throw new BusinessException("Permission denied");
        }
        
        // TODO: Delete related files if necessary
        
        return removeById(id);
    }
    
    /**
     * Batch delete stores
     * 
     * @param ids store IDs
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteStores(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        // Check if user has permission for each store
        if (!StpUtil.hasRole("admin")) {
            Long userId = StpUtil.getLoginIdAsLong();
            for (Long id : ids) {
                Store store = getById(id);
                if (store == null) {
                    throw new BusinessException("Store not found: " + id);
                }
                if (!store.getUid().equals(userId)) {
                    throw new BusinessException("Permission denied for store: " + id);
                }
            }
        }
        
        // TODO: Delete related files if necessary
        
        return removeByIds(ids);
    }
    
    /**
     * Update store status
     * 
     * @param id store ID
     * @param status new status
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long id, String status) {
        // Check if store exists
        Store store = getById(id);
        if (store == null) {
            throw new BusinessException("Store not found");
        }
        
        // Check if user has permission
        if (!StpUtil.hasRole("admin") && !store.getUid().equals(StpUtil.getLoginIdAsLong())) {
            throw new BusinessException("Permission denied");
        }
        
        // Update status
        store.setZhuangtai(status);
        
        return updateById(store);
    }
    
    /**
     * Batch update store status
     * 
     * @param ids store IDs
     * @param status new status
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<Long> ids, String status) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        // Check if user has permission for each store
        if (!StpUtil.hasRole("admin")) {
            Long userId = StpUtil.getLoginIdAsLong();
            for (Long id : ids) {
                Store store = getById(id);
                if (store == null) {
                    throw new BusinessException("Store not found: " + id);
                }
                if (!store.getUid().equals(userId)) {
                    throw new BusinessException("Permission denied for store: " + id);
                }
            }
        }
        
        // Update status for each store
        for (Long id : ids) {
            Store store = new Store();
            store.setId(id);
            store.setZhuangtai(status);
            updateById(store);
        }
        
        return true;
    }
} 