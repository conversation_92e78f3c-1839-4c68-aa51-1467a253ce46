package com.cottontang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Store;
import com.cottontang.entity.Warehouse;
import com.cottontang.exception.BusinessException;
import com.cottontang.mapper.StoreMapper;
import com.cottontang.mapper.WarehouseMapper;
import com.cottontang.service.StoreService;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Store service implementation
 *
 * <AUTHOR>
 */
@Service
public class StoreServiceImpl extends ServiceImpl<StoreMapper, Store> implements StoreService {

    @Autowired
    private WarehouseMapper warehouseMapper;

    /**
     * Get store list with pagination and filtering
     *
     * @param pageQuery pagination and search parameters
     * @return store page
     */
    @Override
    public Page<Store> getStoreList(PageQuery pageQuery) {
        Page<Store> page = new Page<>(pageQuery.getCurrent(), pageQuery.getSize());

        LambdaQueryWrapper<Store> queryWrapper = new LambdaQueryWrapper<>();

        // Search by keyword if provided
        if (pageQuery.hasKeyword()) {
            queryWrapper.like(Store::getPihaoKunkao, pageQuery.getKeyword());
        }

        // Filter by status if provided
        if (pageQuery.hasStatus()) {
            queryWrapper.eq(Store::getZhuangtai, pageQuery.getStatus());
        }

        // Check if current user is admin
        if (StpUtil.hasRole("user")) {
            // If user is not admin, only show their own stores
            queryWrapper.eq(Store::getUid, StpUtil.getLoginIdAsLong());
        }

        // Sort by timestamp descending
        queryWrapper.orderByDesc(Store::getAddTime);

        // Execute query
        Page<Store> result = page(page, queryWrapper);

        // Populate transient fields
//        for (Store store : result.getRecords()) {
//            if (store.getCangkuId() != null) {
//                Warehouse warehouse = warehouseMapper.selectById(store.getCangkuId());
//                if (warehouse != null) {
//                    store.setCangkuName(warehouse.getTitle());
//                }
//            }
//        }

        return result;
    }

    /**
     * Add store
     *
     * @param store store data
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addStore(Store store) {
        // Set current user ID as creator
        store.setUid(StpUtil.getLoginIdAsLong());

        // Set current timestamp
        store.setAddTime(System.currentTimeMillis() / 1000);

        // Check if warehouse exists
//        if (store.getCangkuId() != null) {
//            Warehouse warehouse = warehouseMapper.selectById(store.getCangkuId());
//            if (warehouse == null) {
//                throw new BusinessException("Warehouse not found");
//            }
//        }

        return save(store);
    }

    /**
     * Update store
     *
     * @param store store data
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStore(Store store) {
        // Check if store exists
        Store existing = getById(store.getId());
        if (existing == null) {
            throw new BusinessException("Store not found");
        }

        // Check if user has permission
        if (!StpUtil.hasRole("admin") && !existing.getUid().equals(StpUtil.getLoginIdAsLong())) {
            throw new BusinessException("Permission denied");
        }

        // Check if warehouse exists
//        if (store.getCangkuId() != null) {
//            Warehouse warehouse = warehouseMapper.selectById(store.getCangkuId());
//            if (warehouse == null) {
//                throw new BusinessException("Warehouse not found");
//            }
//        }

        return updateById(store);
    }

    /**
     * Delete store
     *
     * @param id store ID
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteStore(Long id) {
        // Check if store exists
        Store store = getById(id);
        if (store == null) {
            throw new BusinessException("Store not found");
        }

        // Check if user has permission
        if (!StpUtil.hasRole("admin") && !store.getUid().equals(StpUtil.getLoginIdAsLong())) {
            throw new BusinessException("Permission denied");
        }

        // TODO: Delete related files if necessary

        return removeById(id);
    }

    /**
     * Batch delete stores
     *
     * @param ids store IDs
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteStores(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // Check if user has permission for each store
        if (!StpUtil.hasRole("admin")) {
            Long userId = StpUtil.getLoginIdAsLong();
            for (Long id : ids) {
                Store store = getById(id);
                if (store == null) {
                    throw new BusinessException("Store not found: " + id);
                }
                if (!store.getUid().equals(userId)) {
                    throw new BusinessException("Permission denied for store: " + id);
                }
            }
        }

        // TODO: Delete related files if necessary

        return removeByIds(ids);
    }

    /**
     * Update store status
     *
     * @param id store ID
     * @param status new status
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long id, String status) {
        // Check if store exists
        Store store = getById(id);
        if (store == null) {
            throw new BusinessException("Store not found");
        }

        // Check if user has permission
        if (!StpUtil.hasRole("admin") && !store.getUid().equals(StpUtil.getLoginIdAsLong())) {
            throw new BusinessException("Permission denied");
        }

        // Update status
        store.setZhuangtai(status);

        return updateById(store);
    }

    /**
     * Batch update store status
     *
     * @param ids store IDs
     * @param status new status
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<Long> ids, String status) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // Check if user has permission for each store
        if (!StpUtil.hasRole("admin")) {
            Long userId = StpUtil.getLoginIdAsLong();
            for (Long id : ids) {
                Store store = getById(id);
                if (store == null) {
                    throw new BusinessException("Store not found: " + id);
                }
                if (!store.getUid().equals(userId)) {
                    throw new BusinessException("Permission denied for store: " + id);
                }
            }
        }

        // Update status for each store
        for (Long id : ids) {
            Store store = new Store();
            store.setId(id);
            store.setZhuangtai(status);
            updateById(store);
        }

        return true;
    }
}
