package com.cottontang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.entity.SystemSetting;
import java.util.List;
import java.util.Map;

/**
 * 系统设置服务接口
 * 
 * <AUTHOR>
 */
public interface SystemSettingService extends IService<SystemSetting> {
    
    /**
     * 根据键获取设置值
     * 
     * @param key 设置键
     * @return 设置值
     */
    String getSettingValue(String key);
    
    /**
     * 根据键获取设置值，如果不存在则返回默认值
     * 
     * @param key 设置键
     * @param defaultValue 默认值
     * @return 设置值
     */
    String getSettingValue(String key, String defaultValue);
    
    /**
     * 设置配置值
     * 
     * @param key 设置键
     * @param value 设置值
     * @return 如果成功则为true
     */
    boolean setSetting(String key, String value);
    
    /**
     * 批量设置配置
     * 
     * @param settings 设置映射
     * @return 如果成功则为true
     */
    boolean setSettings(Map<String, String> settings);
    
    /**
     * 根据分组获取设置列表
     * 
     * @param group 分组名
     * @return 设置列表
     */
    List<SystemSetting> getSettingsByGroup(String group);
    
    /**
     * 获取所有设置
     * 
     * @return 设置映射
     */
    Map<String, String> getAllSettings();
    
    /**
     * 删除设置
     * 
     * @param key 设置键
     * @return 如果成功则为true
     */
    boolean deleteSetting(String key);
}
