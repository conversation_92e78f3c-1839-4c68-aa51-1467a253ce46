package com.cottontang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.dto.ArticleDTO;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Article;
import com.cottontang.entity.Category;
import com.cottontang.exception.BusinessException;
import com.cottontang.mapper.ArticleMapper;
import com.cottontang.mapper.CategoryMapper;
import com.cottontang.service.ArticleService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Article service implementation
 * 
 * <AUTHOR>
 */
@Service
public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> implements ArticleService {
    
    @Autowired
    private CategoryMapper categoryMapper;
    
    /**
     * Get article list with pagination
     * 
     * @param pageQuery pagination and search parameters
     * @return article page
     */
    @Override
    public Page<Article> getArticleList(PageQuery pageQuery) {
        Page<Article> page = new Page<>(pageQuery.getPage(), pageQuery.getLimit());
        
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        
        // Filter by category ID if provided
        if (pageQuery.getParams().containsKey("catId")) {
            queryWrapper.eq(Article::getCatId, pageQuery.getParams().get("catId"));
        }
        
        // Search by title if keyword provided
        if (pageQuery.getParams().containsKey("keyword")) {
            String keyword = pageQuery.getParams().get("keyword").toString();
            if (StringUtils.hasText(keyword)) {
                queryWrapper.like(Article::getTitle, keyword);
            }
        }
        
        // Sort by order ID (asc) and then by ID (desc)
        queryWrapper.orderByAsc(Article::getOrderId).orderByDesc(Article::getId);
        
        return page(page, queryWrapper);
    }
    
    /**
     * Get articles by category ID
     * 
     * @param catId category ID
     * @return article list
     */
    @Override
    public List<Article> getArticlesByCategoryId(Long catId) {
        // Check if category exists
        Category category = categoryMapper.selectById(catId);
        if (category == null) {
            throw new BusinessException("Category not found");
        }
        
        return list(new LambdaQueryWrapper<Article>()
                .eq(Article::getCatId, catId)
                .orderByAsc(Article::getOrderId)
                .orderByDesc(Article::getId));
    }
    
    /**
     * Get recommended articles
     * 
     * @param limit maximum number of articles to return
     * @return recommended article list
     */
    @Override
    public List<Article> getRecommendedArticles(Integer limit) {
        return list(new LambdaQueryWrapper<Article>()
                .eq(Article::getIsRecom, 1)
                .orderByAsc(Article::getOrderId)
                .orderByDesc(Article::getId)
                .last(limit != null, "LIMIT " + limit));
    }
    
    /**
     * Add article
     * 
     * @param articleDTO article data
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addArticle(ArticleDTO articleDTO) {
        // Check if category exists
        Category category = categoryMapper.selectById(articleDTO.getCatId());
        if (category == null) {
            throw new BusinessException("Category not found");
        }
        
        Article article = new Article();
        BeanUtils.copyProperties(articleDTO, article);
        
        // Set default values
        if (article.getOrderId() == null) {
            article.setOrderId(100);
        }
        
        // Set publication date
        if (articleDTO.getPubDate() != null) {
            article.setPubDate(articleDTO.getPubDate());
            article.setAddTime(articleDTO.getPubDate());
        } else {
            LocalDateTime now = LocalDateTime.now();
            article.setPubDate(now);
            article.setAddTime(now);
        }
        
        // Set default status if not provided
        if (article.getStatus() == null) {
            article.setStatus(1);
        }
        
        return save(article);
    }
    
    /**
     * Update article
     * 
     * @param articleDTO article data
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateArticle(ArticleDTO articleDTO) {
        // Check if article exists
        Article existing = getById(articleDTO.getId());
        if (existing == null) {
            throw new BusinessException("Article not found");
        }
        
        // Check if category exists
        if (articleDTO.getCatId() != null) {
            Category category = categoryMapper.selectById(articleDTO.getCatId());
            if (category == null) {
                throw new BusinessException("Category not found");
            }
        }
        
        Article article = new Article();
        BeanUtils.copyProperties(articleDTO, article);
        
        // Set publication date
        if (articleDTO.getPubDate() != null) {
            article.setPubDate(articleDTO.getPubDate());
            article.setAddTime(articleDTO.getPubDate());
        }
        
        return updateById(article);
    }
    
    /**
     * Delete article
     * 
     * @param id article ID
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteArticle(Long id) {
        // Check if article exists
        Article article = getById(id);
        if (article == null) {
            throw new BusinessException("Article not found");
        }
        
        // TODO: Delete attached files if necessary
        
        return removeById(id);
    }
    
    /**
     * Toggle article recommendation status
     * 
     * @param id article ID
     * @return new recommendation status
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer toggleRecommendation(Long id) {
        // Check if article exists
        Article article = getById(id);
        if (article == null) {
            throw new BusinessException("Article not found");
        }
        
        // Toggle recommendation status
        Integer newStatus = article.getIsRecom() == 1 ? 0 : 1;
        article.setIsRecom(newStatus);
        
        updateById(article);
        
        return newStatus;
    }
    
    /**
     * Update article order ID
     * 
     * @param id article ID
     * @param orderId new order ID
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderId(Long id, Integer orderId) {
        // Check if article exists
        Article article = getById(id);
        if (article == null) {
            throw new BusinessException("Article not found");
        }
        
        article.setOrderId(orderId);
        
        return updateById(article);
    }
} 