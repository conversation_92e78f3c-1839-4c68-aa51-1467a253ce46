package com.cottontang.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.dto.*;
import com.cottontang.entity.LoginRecord;
import com.cottontang.entity.User;
import com.cottontang.exception.BusinessException;
import com.cottontang.mapper.UserMapper;
import com.cottontang.service.LoginRecordService;
import com.cottontang.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;

/**
 * 用户服务实现
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final LoginRecordService loginRecordService;

    @Override
    public String login(LoginDTO loginDTO) {
        // 根据用户名查找用户
        User user = getOne(new LambdaQueryWrapper<User>()
                .eq(User::getUsername, loginDTO.getUsername()));

        // 验证用户是否存在
        if (user == null) {
            throw new BusinessException("用户名或密码错误");
        }

        // 验证密码
        if (!SaSecureUtil.md5(loginDTO.getPassword()).equals(user.getPassword())) {
            throw new BusinessException("用户名或密码错误");
        }

        // 检查用户状态
        if (user.getStatus() != null && user.getStatus() != 1) {
            throw new BusinessException("用户已被禁用");
        }

        // 检查审核状态
        if (user.getIsCheck() != null && user.getIsCheck() != 1) {
            throw new BusinessException("您的账户正在审核中");
        }

        // 设置登录ID（会话ID）
        StpUtil.login(user.getId());

        // 更新最后登录时间
        User updateUser = new User();
        updateUser.setId(user.getId());
        updateUser.setLastLogin(user.getLoginTime());
        updateUser.setLoginTime(System.currentTimeMillis() / 1000);
        updateUser.setLastIp(getClientIp());
        updateById(updateUser);

        // 记录登录日志
        LoginRecord loginRecord = new LoginRecord();
        loginRecord.setUid(user.getId());
        loginRecord.setUidText(user.getUsername());
        loginRecord.setAddTime(System.currentTimeMillis() / 1000);
        loginRecord.setDesc("用户登录成功");
        loginRecordService.save(loginRecord);

        // 返回令牌
        return StpUtil.getTokenValue();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean register(RegisterDTO registerDTO) {
        // 验证密码确认
        if (!registerDTO.getPassword().equals(registerDTO.getConfirmPassword())) {
            throw new BusinessException("两次输入的密码不一致");
        }

        // 检查用户名是否已存在
        if (checkUsernameExists(registerDTO.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        // 创建新用户
        User user = new User();
        BeanUtil.copyProperties(registerDTO, user);

        // 设置默认值
        user.setPassword(SaSecureUtil.md5(registerDTO.getPassword()));
        user.setAddTime(System.currentTimeMillis() / 1000);
        user.setStatus(1); // 默认启用
        user.setIsCheck(0); // 默认未审核
        user.setType(1); // 普通用户
        user.setMemberType(0); // 普通会员

        if (StrUtil.isBlank(user.getNickname())) {
            user.setNickname(user.getUsername());
        }

        return save(user);
    }

    @Override
    public UserInfoDTO getUserInfo(Long userId) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        UserInfoDTO userInfo = new UserInfoDTO();
        BeanUtil.copyProperties(user, userInfo);
        userInfo.setUserId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setRoles(new String[]{"user"}); // 普通用户角色

        return userInfo;
    }

    @Override
    public void logout() {
        StpUtil.logout();
    }

    @Override
    public Page<User> getUserList(PageQuery pageQuery) {
        Page<User> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());

        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();

        // 搜索条件
        if (StrUtil.isNotBlank(pageQuery.getKeyword())) {
            wrapper.like(User::getUsername, pageQuery.getKeyword())
                   .or()
                   .like(User::getNickname, pageQuery.getKeyword())
                   .or()
                   .like(User::getCompany, pageQuery.getKeyword());
        }

        // 排序
        wrapper.orderByDesc(User::getAddTime);

        return this.page(page, wrapper);
    }

    @Override
    public boolean updateUser(User user) {
        return updateById(user);
    }

    @Override
    public boolean deleteUser(Long id) {
        return removeById(id);
    }

    @Override
    public boolean checkUser(Long id, Integer isCheck) {
        User user = new User();
        user.setId(id);
        user.setIsCheck(isCheck);
        return updateById(user);
    }

    @Override
    public boolean checkUsernameExists(String username) {
        return count(new LambdaQueryWrapper<User>()
                .eq(User::getUsername, username)) > 0;
    }

    @Override
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 验证旧密码
        if (!SaSecureUtil.md5(oldPassword).equals(user.getPassword())) {
            throw new BusinessException("原密码错误");
        }

        // 更新密码
        User updateUser = new User();
        updateUser.setId(userId);
        updateUser.setPassword(SaSecureUtil.md5(newPassword));

        return updateById(updateUser);
    }

    @Override
    public boolean createUser(User user) {
        // 检查用户名是否已存在
        if (checkUsernameExists(user.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        // 设置默认值
        user.setPassword(SaSecureUtil.md5(user.getPassword()));
        user.setAddTime(System.currentTimeMillis() / 1000);
        user.setIsCheck(0); // 默认未审核
        user.setStatus(1); // 默认启用

        return save(user);
    }

    @Override
    public boolean resetPassword(Long id, String password) {
        User updateUser = new User();
        updateUser.setId(id);
        updateUser.setPassword(SaSecureUtil.md5(password));

        return updateById(updateUser);
    }

    @Override
    public boolean batchDelete(Long[] ids) {
        if (ids == null || ids.length == 0) {
            return false;
        }

        return removeByIds(Arrays.asList(ids));
    }

    @Override
    public void forgetPassword(ForgetPasswordDTO forgetPasswordDTO) {

    }

    /**
     * 获取客户端IP地址（简化实现）
     */
    private String getClientIp() {
        // 这里应该从HttpServletRequest中获取真实IP
        // 简化实现，返回默认值
        return "127.0.0.1";
    }
}
