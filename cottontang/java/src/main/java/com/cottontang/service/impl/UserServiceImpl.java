package com.cottontang.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.dto.*;
import com.cottontang.entity.LoginRecord;
import com.cottontang.entity.User;
import com.cottontang.exception.BusinessException;
import com.cottontang.mapper.UserMapper;
import com.cottontang.service.LoginRecordService;
import com.cottontang.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpSession;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户服务实现
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final LoginRecordService loginRecordService;

    @Override
    public Map<String, Object> login(LoginDTO loginDTO) {
        // 完全按照PHP逻辑：验证用户名和密码不能为空
        if (StrUtil.isBlank(loginDTO.getUsername()) || StrUtil.isBlank(loginDTO.getPassword())) {
            throw new BusinessException("请输入用户名密码");
        }

        // 根据用户名查找用户
        User user = getOne(new LambdaQueryWrapper<User>()
                .eq(User::getUsername, loginDTO.getUsername()));

        // 验证用户是否存在
        if (user == null) {
            throw new BusinessException("用户名或密码错误");
        }

        // 验证密码（使用MD5加密，与PHP一致）
        if (!SaSecureUtil.md5(loginDTO.getPassword()).equals(user.getPassword())) {
            throw new BusinessException("用户名或密码错误");
        }

        // 检查审核状态（完全按照PHP逻辑）
        if (user.getIsCheck() == null || user.getIsCheck() == 0) {
            throw new BusinessException("您的帐户正在审核中");
        }

        // 设置登录ID（会话ID）
        StpUtil.login(user.getId());

        // 更新最后登录时间（完全按照PHP逻辑）
        User updateUser = new User();
        updateUser.setId(user.getId());
        updateUser.setLastLogin(user.getLoginTime());
        updateUser.setLoginTime(System.currentTimeMillis() / 1000);
        updateUser.setLastIp(getClientIp());
        updateById(updateUser);

        // 记录登录日志（对应PHP的M('Denglu')->add）
        LoginRecord loginRecord = new LoginRecord();
        loginRecord.setUid(user.getId());
        loginRecord.setUidText(user.getUsername());
        loginRecord.setAddTime(System.currentTimeMillis() / 1000);
        loginRecordService.save(loginRecord);

        // 获取用户信息
        UserInfoDTO userInfo = getUserInfo(user.getId());

        // 返回结果（完全按照PHP格式）
        Map<String, Object> result = new HashMap<>();
        result.put("code", 1);
        result.put("msg", "登录成功");
        result.put("token", StpUtil.getTokenValue());
        result.put("userInfo", userInfo);
        result.put("url", "/"); // 默认跳转到首页

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> register(RegisterDTO registerDTO) {
        // 检查用户名是否已存在（完全按照PHP逻辑）
        if (checkUsernameExists(registerDTO.getUsername())) {
            throw new BusinessException("手机号已被注册，请换其他手机号");
        }

        // 验证手机验证码（完全按照PHP逻辑）
        if (!validateMobileCode(registerDTO.getUsername(), registerDTO.getMobile_code())) {
            throw new BusinessException("手机验证码错误");
        }

        // 创建新用户
        User user = new User();
        user.setUsername(registerDTO.getUsername());
        user.setNickname(registerDTO.getNickname());
        user.setCompany(registerDTO.getCompany());
        user.setPassword(SaSecureUtil.md5(registerDTO.getPassword()));
        user.setAddTime(System.currentTimeMillis() / 1000);
        user.setStatus(1); // 默认启用
        user.setIsCheck(0); // 默认未审核
        user.setType(1); // 普通用户
        user.setMemberType(0); // 普通会员

        boolean success = save(user);

        if (success) {
            // 返回结果（完全按照PHP格式）
            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("msg", "注册成功，请等待审核");
            result.put("url", "/login"); // 跳转到登录页面
            return result;
        } else {
            throw new BusinessException("注册失败，请您重新注册");
        }
    }

    @Override
    public UserInfoDTO getUserInfo(Long userId) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        UserInfoDTO userInfo = new UserInfoDTO();
        BeanUtil.copyProperties(user, userInfo);
        userInfo.setUserId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setRoles(new String[]{"user"}); // 普通用户角色

        return userInfo;
    }

    @Override
    public void logout() {
        StpUtil.logout();
    }

    @Override
    public Page<User> getUserList(PageQuery pageQuery) {
        Page<User> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());

        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();

        // 搜索条件
        if (StrUtil.isNotBlank(pageQuery.getKeyword())) {
            wrapper.like(User::getUsername, pageQuery.getKeyword())
                   .or()
                   .like(User::getNickname, pageQuery.getKeyword())
                   .or()
                   .like(User::getCompany, pageQuery.getKeyword());
        }

        // 排序
        wrapper.orderByDesc(User::getAddTime);

        return this.page(page, wrapper);
    }

    @Override
    public boolean updateUser(User user) {
        return updateById(user);
    }

    @Override
    public boolean deleteUser(Long id) {
        return removeById(id);
    }

    @Override
    public boolean checkUser(Long id, Integer isCheck) {
        User user = new User();
        user.setId(id);
        user.setIsCheck(isCheck);
        return updateById(user);
    }

    @Override
    public boolean checkUsernameExists(String username) {
        return count(new LambdaQueryWrapper<User>()
                .eq(User::getUsername, username)) > 0;
    }

    @Override
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 验证旧密码
        if (!SaSecureUtil.md5(oldPassword).equals(user.getPassword())) {
            throw new BusinessException("原密码错误");
        }

        // 更新密码
        User updateUser = new User();
        updateUser.setId(userId);
        updateUser.setPassword(SaSecureUtil.md5(newPassword));

        return updateById(updateUser);
    }

    @Override
    public boolean createUser(User user) {
        // 检查用户名是否已存在
        if (checkUsernameExists(user.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        // 设置默认值
        user.setPassword(SaSecureUtil.md5(user.getPassword()));
        user.setAddTime(System.currentTimeMillis() / 1000);
        user.setIsCheck(0); // 默认未审核
        user.setStatus(1); // 默认启用

        return save(user);
    }

    @Override
    public boolean resetPassword(Long id, String password) {
        User updateUser = new User();
        updateUser.setId(id);
        updateUser.setPassword(SaSecureUtil.md5(password));

        return updateById(updateUser);
    }

    @Override
    public boolean batchDelete(Long[] ids) {
        if (ids == null || ids.length == 0) {
            return false;
        }

        return removeByIds(Arrays.asList(ids));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> forgetPassword(ForgetPasswordDTO forgetPasswordDTO) {
        // 验证手机验证码（完全按照PHP逻辑）
        if (!validateMobileCode(forgetPasswordDTO.getUsername(), forgetPasswordDTO.getMobile_code())) {
            throw new BusinessException("手机验证码错误");
        }

        // 根据用户名查找用户
        User user = getOne(new LambdaQueryWrapper<User>()
                .eq(User::getUsername, forgetPasswordDTO.getUsername()));

        if (user != null) {
            // 更新密码（完全按照PHP逻辑：MD5加密）
            User updateUser = new User();
            updateUser.setId(user.getId());
            updateUser.setPassword(SaSecureUtil.md5(forgetPasswordDTO.getPassword()));

            boolean success = updateById(updateUser);

            if (success) {
                // 返回结果（完全按照PHP格式）
                Map<String, Object> result = new HashMap<>();
                result.put("code", 1);
                result.put("msg", "密码修改成功，请登录");
                result.put("url", "/login"); // 跳转到登录页面
                return result;
            } else {
                throw new BusinessException("密码修改失败");
            }
        } else {
            throw new BusinessException("手机号错误");
        }
    }

    /**
     * 验证手机验证码 - 完全按照PHP逻辑
     *
     * @param mobile 手机号
     * @param code 验证码
     * @return 验证结果
     */
    private boolean validateMobileCode(String mobile, String code) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpSession session = attributes.getRequest().getSession();
                String sessionCode = (String) session.getAttribute("mobile_code");
                String sessionMobile = (String) session.getAttribute("mobile_number");

                // 完全按照PHP逻辑验证
                if (code.equals(sessionCode) && mobile.equals(sessionMobile)) {
                    // 验证成功后清除session（按照PHP逻辑）
                    session.removeAttribute("mobile_code");
                    session.removeAttribute("mobile_number");
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取客户端IP地址（简化实现）
     */
    private String getClientIp() {
        // 这里应该从HttpServletRequest中获取真实IP
        // 简化实现，返回默认值
        return "127.0.0.1";
    }
}
