package com.cottontang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.LoginRecord;
import com.cottontang.mapper.LoginRecordMapper;
import com.cottontang.service.LoginRecordService;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;

/**
 * 登录记录服务实现
 * 
 * <AUTHOR>
 */
@Service
public class LoginRecordServiceImpl extends ServiceImpl<LoginRecordMapper, LoginRecord> implements LoginRecordService {
    
    @Override
    public Page<LoginRecord> getLoginRecordList(PageQuery pageQuery) {
        Page<LoginRecord> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        
        LambdaQueryWrapper<LoginRecord> wrapper = new LambdaQueryWrapper<>();
        
        // 搜索条件
        if (StrUtil.isNotBlank(pageQuery.getKeyword())) {
            wrapper.like(LoginRecord::getUidText, pageQuery.getKeyword())
                   .or()
                   .like(LoginRecord::getIpAddress, pageQuery.getKeyword());
        }
        
        // 排序
        wrapper.orderByDesc(LoginRecord::getAddTime);
        
        return this.page(page, wrapper);
    }
    
    @Override
    public Page<LoginRecord> getLoginRecordsByUserId(Long uid, PageQuery pageQuery) {
        Page<LoginRecord> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        
        LambdaQueryWrapper<LoginRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LoginRecord::getUid, uid);
        
        // 排序
        wrapper.orderByDesc(LoginRecord::getAddTime);
        
        return this.page(page, wrapper);
    }
    
    @Override
    public int cleanExpiredRecords(int days) {
        long expiredTime = System.currentTimeMillis() / 1000 - (days * 24 * 60 * 60);
        
        LambdaQueryWrapper<LoginRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(LoginRecord::getAddTime, expiredTime);
        
        return (int) this.count(wrapper);
    }
}
