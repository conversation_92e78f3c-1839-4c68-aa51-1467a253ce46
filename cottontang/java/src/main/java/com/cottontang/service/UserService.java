package com.cottontang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.dto.PageQuery;
import com.cottontang.dto.LoginDTO;
import com.cottontang.dto.RegisterDTO;
import com.cottontang.dto.UserInfoDTO;
import com.cottontang.entity.User;

/**
 * 用户服务接口
 * 
 * <AUTHOR>
 */
public interface UserService extends IService<User> {
    
    /**
     * 用户登录
     * 
     * @param loginDTO 登录参数
     * @return 令牌字符串
     */
    String login(LoginDTO loginDTO);
    
    /**
     * 用户注册
     * 
     * @param registerDTO 注册参数
     * @return 如果成功则为true
     */
    boolean register(RegisterDTO registerDTO);
    
    /**
     * 根据ID获取用户信息
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    UserInfoDTO getUserInfo(Long userId);
    
    /**
     * 登出当前用户
     */
    void logout();
    
    /**
     * 获取用户列表（分页）
     * 
     * @param pageQuery 分页和搜索参数
     * @return 用户分页
     */
    Page<User> getUserList(PageQuery pageQuery);
    
    /**
     * 更新用户信息
     * 
     * @param user 用户数据
     * @return 如果成功则为true
     */
    boolean updateUser(User user);
    
    /**
     * 根据ID删除用户
     * 
     * @param id 用户ID
     * @return 如果成功则为true
     */
    boolean deleteUser(Long id);
    
    /**
     * 审核用户
     * 
     * @param id 用户ID
     * @param isCheck 审核状态（0: 未审核, 1: 已审核）
     * @return 如果成功则为true
     */
    boolean checkUser(Long id, Integer isCheck);
    
    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 如果存在则为true
     */
    boolean checkUsernameExists(String username);
    
    /**
     * 修改密码
     *
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 如果成功则为true
     */
    boolean changePassword(Long userId, String oldPassword, String newPassword);

    /**
     * 创建用户
     *
     * @param user 用户信息
     * @return 如果成功则为true
     */
    boolean createUser(User user);

    /**
     * 重置用户密码
     *
     * @param id 用户ID
     * @param password 新密码
     * @return 如果成功则为true
     */
    boolean resetPassword(Long id, String password);

    /**
     * 批量删除用户
     *
     * @param ids 用户ID数组
     * @return 如果成功则为true
     */
    boolean batchDelete(Long[] ids);
}
