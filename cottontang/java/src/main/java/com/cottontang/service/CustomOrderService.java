package com.cottontang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.common.PageQuery;
import com.cottontang.entity.CustomOrder;

/**
 * 定制订单服务接口
 * 
 * <AUTHOR>
 */
public interface CustomOrderService extends IService<CustomOrder> {
    
    /**
     * 获取定制订单列表（分页）
     * 
     * @param pageQuery 分页和搜索参数
     * @return 定制订单分页
     */
    Page<CustomOrder> getCustomOrderList(PageQuery pageQuery);
    
    /**
     * 添加定制订单
     * 
     * @param customOrder 定制订单数据
     * @return 如果成功则为true
     */
    boolean addCustomOrder(CustomOrder customOrder);
    
    /**
     * 更新定制订单
     * 
     * @param customOrder 定制订单数据
     * @return 如果成功则为true
     */
    boolean updateCustomOrder(CustomOrder customOrder);
    
    /**
     * 根据ID删除定制订单
     * 
     * @param id 定制订单ID
     * @return 如果成功则为true
     */
    boolean deleteCustomOrder(Long id);
    
    /**
     * 根据用户ID获取定制订单列表
     * 
     * @param uid 用户ID
     * @param pageQuery 分页参数
     * @return 定制订单分页
     */
    Page<CustomOrder> getCustomOrdersByUserId(Long uid, PageQuery pageQuery);
    
    /**
     * 更新订单状态
     * 
     * @param id 订单ID
     * @param status 新状态
     * @return 如果成功则为true
     */
    boolean updateOrderStatus(Long id, Integer status);
}
