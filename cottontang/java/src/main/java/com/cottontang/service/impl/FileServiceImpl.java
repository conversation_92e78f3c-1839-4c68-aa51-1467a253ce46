package com.cottontang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.config.FileProperties;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.File;
import com.cottontang.exception.BusinessException;
import com.cottontang.mapper.FileMapper;
import com.cottontang.service.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * File service implementation
 * 
 * <AUTHOR>
 */
@Service
public class FileServiceImpl extends ServiceImpl<FileMapper, File> implements FileService {

    @Autowired
    private FileProperties fileProperties;
    
    /**
     * Upload file
     * 
     * @param file file to upload
     * @param uploaderId uploader ID
     * @param uploaderType uploader type (1: admin, 2: user)
     * @return uploaded file info
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> uploadFile(MultipartFile file, Long uploaderId, Integer uploaderType) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("Please select a file to upload");
        }
        
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new BusinessException("Invalid file name");
        }
        
        // Validate file extension
        String extension = StringUtils.getFilenameExtension(originalFilename);
        if (extension == null || !fileProperties.getAllowedExtensionsList().contains(extension.toLowerCase())) {
            throw new BusinessException("File type not allowed. Allowed types: " + fileProperties.getAllowedExtensions());
        }
        
        // Validate file size
        if (file.getSize() > fileProperties.getMaxSize()) {
            throw new BusinessException("File size exceeds the maximum allowed size");
        }
        
        try {
            // Generate date-based directory structure
            String dateDir = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String relativePath = dateDir + "/";
            
            // Create directory if it doesn't exist
            Path uploadPath = Paths.get(fileProperties.getPath() + relativePath);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
            
            // Generate unique filename
            String storedFilename = UUID.randomUUID().toString().replaceAll("-", "") + "." + extension;
            
            // Save the file
            Path filePath = uploadPath.resolve(storedFilename);
            Files.copy(file.getInputStream(), filePath);
            
            // Determine file type
            Integer fileType = 3; // default: other
            if (file.getContentType() != null) {
                if (file.getContentType().startsWith("image/")) {
                    fileType = 1; // image
                } else if (file.getContentType().contains("document") 
                        || file.getContentType().contains("pdf")
                        || file.getContentType().contains("excel")
                        || file.getContentType().contains("word")) {
                    fileType = 2; // document
                }
            }
            
            // Save file info to database
            File fileEntity = new File();
            fileEntity.setOriginalName(originalFilename);
            fileEntity.setStoredName(storedFilename);
            fileEntity.setFilePath(relativePath + storedFilename);
            fileEntity.setFileSize(file.getSize());
            fileEntity.setExtension(extension);
            fileEntity.setContentType(file.getContentType());
            fileEntity.setUploaderId(uploaderId);
            fileEntity.setUploaderType(uploaderType);
            fileEntity.setFileType(fileType);
            
            save(fileEntity);
            
            // Return file info
            Map<String, Object> result = new HashMap<>();
            result.put("id", fileEntity.getId());
            result.put("originalName", fileEntity.getOriginalName());
            result.put("filePath", fileEntity.getFilePath());
            result.put("url", "/" + fileProperties.getPath() + fileEntity.getFilePath());
            result.put("fileSize", fileEntity.getFileSize());
            result.put("contentType", fileEntity.getContentType());
            
            return result;
            
        } catch (IOException e) {
            throw new BusinessException("Failed to upload file: " + e.getMessage());
        }
    }
    
    /**
     * Get file list with pagination
     * 
     * @param pageQuery pagination and search parameters
     * @return file page
     */
    @Override
    public Page<File> getFileList(PageQuery pageQuery) {
        Page<File> page = new Page<>(pageQuery.getPage(), pageQuery.getLimit());
        
        LambdaQueryWrapper<File> queryWrapper = new LambdaQueryWrapper<>();
        
        // Filter by file type if provided
        if (pageQuery.getParams().containsKey("fileType")) {
            queryWrapper.eq(File::getFileType, pageQuery.getParams().get("fileType"));
        }
        
        // Filter by uploader type if provided
        if (pageQuery.getParams().containsKey("uploaderType")) {
            queryWrapper.eq(File::getUploaderType, pageQuery.getParams().get("uploaderType"));
        }
        
        // Filter by uploader ID if provided
        if (pageQuery.getParams().containsKey("uploaderId")) {
            queryWrapper.eq(File::getUploaderId, pageQuery.getParams().get("uploaderId"));
        }
        
        // Search by file name if keyword provided
        if (pageQuery.getParams().containsKey("keyword")) {
            String keyword = pageQuery.getParams().get("keyword").toString();
            if (StringUtils.hasText(keyword)) {
                queryWrapper.like(File::getOriginalName, keyword);
            }
        }
        
        // Sort by creation date (desc)
        queryWrapper.orderByDesc(File::getCreateTime);
        
        return page(page, queryWrapper);
    }
    
    /**
     * Get file by ID
     * 
     * @param id file ID
     * @return file info
     */
    @Override
    public File getFileById(Long id) {
        File file = getById(id);
        if (file == null) {
            throw new BusinessException("File not found");
        }
        return file;
    }
    
    /**
     * Delete file
     * 
     * @param id file ID
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFile(Long id) {
        File file = getById(id);
        if (file == null) {
            throw new BusinessException("File not found");
        }
        
        try {
            // Delete file from disk
            Path filePath = Paths.get(fileProperties.getPath() + file.getFilePath());
            if (Files.exists(filePath)) {
                Files.delete(filePath);
            }
            
            // Delete file record from database
            return removeById(id);
            
        } catch (IOException e) {
            throw new BusinessException("Failed to delete file: " + e.getMessage());
        }
    }
    
    /**
     * Batch delete files
     * 
     * @param ids file IDs
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteFiles(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        for (Long id : ids) {
            deleteFile(id);
        }
        
        return true;
    }
} 