package com.cottontang.config;

import cn.dev33.satoken.jwt.StpLogicJwtForStateless;
import cn.dev33.satoken.stp.StpLogic;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Sa-Token认证配置
 * 
 * <AUTHOR>
 */
@Configuration
public class SaTokenConfig {
    
    /**
     * Sa-Token整合JWT（无状态模式）
     */
    @Bean
    public StpLogic getStpLogicJwt() {
        return new StpLogicJwtForStateless();
    }
} 