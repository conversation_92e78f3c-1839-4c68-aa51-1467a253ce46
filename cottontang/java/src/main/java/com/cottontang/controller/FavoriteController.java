package com.cottontang.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cottontang.common.PageQuery;
import com.cottontang.common.Result;
import com.cottontang.entity.Favorite;
import com.cottontang.service.FavoriteService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 收藏控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/favorites")
@RequiredArgsConstructor
@SaCheckLogin
public class FavoriteController {
    
    private final FavoriteService favoriteService;
    
    /**
     * 添加收藏
     * 
     * @param favorId 收藏对象ID
     * @param favorType 收藏类型
     * @return 操作结果
     */
    @PostMapping
    public Result<String> addFavorite(@RequestParam Long favorId, @RequestParam Integer favorType) {
        Long uid = StpUtil.getLoginIdAsLong();
        
        if (favoriteService.isFavorited(uid, favorId)) {
            return Result.error("已收藏过该条记录");
        }
        
        boolean success = favoriteService.addFavorite(uid, favorId, favorType);
        return success ? Result.success("收藏成功") : Result.error("收藏失败");
    }
    
    /**
     * 取消收藏
     * 
     * @param favorId 收藏对象ID
     * @return 操作结果
     */
    @DeleteMapping("/{favorId}")
    public Result<String> removeFavorite(@PathVariable Long favorId) {
        Long uid = StpUtil.getLoginIdAsLong();
        
        boolean success = favoriteService.removeFavorite(uid, favorId);
        return success ? Result.success("取消收藏成功") : Result.error("取消收藏失败");
    }
    
    /**
     * 检查是否已收藏
     * 
     * @param favorId 收藏对象ID
     * @return 是否已收藏
     */
    @GetMapping("/check/{favorId}")
    public Result<Boolean> checkFavorite(@PathVariable Long favorId) {
        Long uid = StpUtil.getLoginIdAsLong();
        boolean isFavorited = favoriteService.isFavorited(uid, favorId);
        return Result.success(isFavorited);
    }
    
    /**
     * 获取用户收藏列表
     * 
     * @param favorType 收藏类型（可选）
     * @param pageQuery 分页参数
     * @return 收藏分页
     */
    @GetMapping("/list")
    public Result<Page<Favorite>> getUserFavorites(@RequestParam(required = false) Integer favorType, 
                                                   PageQuery pageQuery) {
        Long uid = StpUtil.getLoginIdAsLong();
        Page<Favorite> favorites = favoriteService.getUserFavorites(uid, favorType, pageQuery);
        return Result.success(favorites);
    }
    
    /**
     * 获取收藏数量
     * 
     * @param favorId 收藏对象ID
     * @return 收藏数量
     */
    @GetMapping("/count/{favorId}")
    public Result<Long> getFavoriteCount(@PathVariable Long favorId) {
        long count = favoriteService.getFavoriteCount(favorId);
        return Result.success(count);
    }
}
