package com.cottontang.controller;

import com.cottontang.common.Result;
import com.cottontang.entity.Category;
import com.cottontang.service.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分类控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/categories")
public class CategoryController {
    
    @Autowired
    private CategoryService categoryService;
    
    /**
     * 获取分类树
     * 
     * @return 分类树
     */
    @GetMapping("/tree")
    public Result<List<Category>> getCategoryTree() {
        return Result.success(categoryService.getCategoryTree());
    }
    
    /**
     * 根据父ID获取分类
     * 
     * @param pid 父ID
     * @return 分类列表
     */
    @GetMapping("/list")
    public Result<List<Category>> getCategoriesByParentId(@RequestParam(defaultValue = "0") Long pid) {
        return Result.success(categoryService.getCategoriesByParentId(pid));
    }
    
    /**
     * 根据ID获取分类
     * 
     * @param id 分类ID
     * @return 分类
     */
    @GetMapping("/{id}")
    public Result<Category> getCategoryById(@PathVariable Long id) {
        return Result.success(categoryService.getById(id));
    }
    
    /**
     * 添加分类
     * 
     * @param category 分类数据
     * @return 结果
     */
    @PostMapping
    public Result<Boolean> addCategory(@RequestBody Category category) {
        return Result.success(categoryService.addCategory(category));
    }
    
    /**
     * 更新分类
     * 
     * @param id 分类ID
     * @param category 分类数据
     * @return 结果
     */
    @PutMapping("/{id}")
    public Result<Boolean> updateCategory(@PathVariable Long id, @RequestBody Category category) {
        category.setId(id);
        return Result.success(categoryService.updateCategory(category));
    }
    
    /**
     * 删除分类
     * 
     * @param id 分类ID
     * @return 结果
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteCategory(@PathVariable Long id) {
        return Result.success(categoryService.deleteCategory(id));
    }
} 