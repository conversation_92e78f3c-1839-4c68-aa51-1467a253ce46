package com.cottontang.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.cottontang.common.Result;
import com.cottontang.entity.Region;
import com.cottontang.service.RegionService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 区域控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/region")
@RequiredArgsConstructor
public class RegionController {
    
    private final RegionService regionService;
    
    /**
     * 根据父ID获取区域列表
     * 
     * @param pid 父ID（0表示顶层）
     * @return 区域列表
     */
    @GetMapping("/list/{pid}")
    @SaCheckRole("admin")
    public Result<List<Region>> getByParentId(@PathVariable Long pid) {
        return Result.success(regionService.getRegionsByParentId(pid));
    }
    
    /**
     * 获取区域树
     * 
     * @return 区域树
     */
    @GetMapping("/tree")
    @SaCheckRole("admin")
    public Result<List<Region>> getTree() {
        return Result.success(regionService.getRegionTree());
    }
    
    /**
     * 根据ID获取区域
     * 
     * @param id 区域ID
     * @return 区域详情
     */
    @GetMapping("/{id}")
    @SaCheckRole("admin")
    public Result<Region> getById(@PathVariable Long id) {
        return Result.success(regionService.getById(id));
    }
    
    /**
     * 添加区域
     * 
     * @param region 区域数据
     * @return 成功或错误
     */
    @PostMapping
    @SaCheckRole("admin")
    public Result<Boolean> add(@Valid @RequestBody Region region) {
        return Result.success(regionService.addRegion(region));
    }
    
    /**
     * 更新区域
     * 
     * @param region 区域数据
     * @return 成功或错误
     */
    @PutMapping
    @SaCheckRole("admin")
    public Result<Boolean> update(@Valid @RequestBody Region region) {
        return Result.success(regionService.updateRegion(region));
    }
    
    /**
     * 删除区域
     * 
     * @param id 区域ID
     * @return 成功或错误
     */
    @DeleteMapping("/{id}")
    @SaCheckRole("admin")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.success(regionService.deleteRegion(id));
    }
} 