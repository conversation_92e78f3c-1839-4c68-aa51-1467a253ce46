package com.cottontang.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cottontang.common.PageData;
import com.cottontang.common.Result;
import com.cottontang.dto.PageQuery;
import com.cottontang.dto.StoreDTO;
import com.cottontang.entity.Store;
import com.cottontang.service.StoreService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商城控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/stores")
public class StoreController {

    @Autowired
    private StoreService storeService;

    /**
     * 获取商品列表（分页和过滤）
     *
     * @param pageQuery 分页和搜索参数
     * @return 商品分页
     */
    @GetMapping("/page")
    public Result<PageData<Store>> getStorePage(PageQuery pageQuery) {
        Page<Store> page = storeService.getStoreList(pageQuery);
        return Result.success(new PageData<>(page));
    }

    /**
     * 根据ID获取商品
     *
     * @param id 商品ID
     * @return 商品
     */
    @GetMapping("/{id}")
    public Result<Store> getStoreById(@PathVariable Long id) {
        return Result.success(storeService.getById(id));
    }

    /**
     * 添加商品
     *
     * @param storeDTO 商品数据
     * @return 结果
     */
    @PostMapping
    public Result<Boolean> addStore(@RequestBody StoreDTO storeDTO) {
        Store store = new Store();
        BeanUtils.copyProperties(storeDTO, store);
        return Result.success(storeService.addStore(store));
    }

    /**
     * 更新商品
     *
     * @param id 商品ID
     * @param storeDTO 商品数据
     * @return 结果
     */
    @PutMapping("/{id}")
    public Result<Boolean> updateStore(@PathVariable Long id, @RequestBody StoreDTO storeDTO) {
        storeDTO.setId(id);
        Store store = new Store();
        BeanUtils.copyProperties(storeDTO, store);
        return Result.success(storeService.updateStore(store));
    }

    /**
     * 删除商品
     *
     * @param id 商品ID
     * @return 结果
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteStore(@PathVariable Long id) {
        return Result.success(storeService.deleteStore(id));
    }

    /**
     * 批量删除商品
     *
     * @param ids 商品ID列表
     * @return 结果
     */
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteStores(@RequestBody List<Long> ids) {
        return Result.success(storeService.batchDeleteStores(ids));
    }

    /**
     * 更新商品状态
     *
     * @param id 商品ID
     * @param status 新状态
     * @return 结果
     */
    @PutMapping("/{id}/status")
    public Result<Boolean> updateStatus(@PathVariable Long id, @RequestParam String status) {
        return Result.success(storeService.updateStatus(id, status));
    }

    /**
     * 批量更新商品状态
     *
     * @param ids 商品ID列表
     * @param status 新状态
     * @return 结果
     */
    @PutMapping("/batch/status")
    public Result<Boolean> batchUpdateStatus(@RequestBody List<Long> ids, @RequestParam String status) {
        return Result.success(storeService.batchUpdateStatus(ids, status));
    }
}
