package com.cottontang.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cottontang.common.PageData;
import com.cottontang.common.Result;
import com.cottontang.dto.PageQuery;
import com.cottontang.dto.StoreDTO;
import com.cottontang.entity.Store;
import com.cottontang.service.StoreService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 商城控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/stores")
public class StoreController {

    @Autowired
    private StoreService storeService;

    /**
     * 获取筛选选项 - 完全按照PHP原版实现
     * 对应PHP的_initialize方法中的筛选选项获取
     */
    @GetMapping("/filter-options")
    public Result<Map<String, Object>> getFilterOptions(@RequestParam(defaultValue = "1") Integer def) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 类型选项 - 完全按照PHP原版配置 C('xls_leixing')
            List<String> xlsLeixing = Arrays.asList("手摘棉", "机采棉");
            result.put("xls_leixing", xlsLeixing);

            // 年度选项 - 完全按照PHP原版配置 C('xls_niandu')
            List<Integer> xlsNiandu = Arrays.asList(2024, 2023, 2022);
            result.put("xls_niandu", xlsNiandu);

            // 状态选项 - 完全按照PHP原版配置 C('xls_zhuangtai')
            List<String> xlsZhuangtai = Arrays.asList("挂单", "已售", "在途");
            result.put("xls_zhuangtai", xlsZhuangtai);

            // 大导航，新疆棉 地产棉 国储棉 进口棉 - 完全按照PHP原版
            // TODO: 从数据库Cat表获取pid=0的记录
            List<Map<String, Object>> chandi = new ArrayList<>();
            Map<String, Object> xinjiang = new HashMap<>();
            xinjiang.put("id", 1);
            xinjiang.put("name", "新疆棉");
            chandi.add(xinjiang);

            Map<String, Object> dichan = new HashMap<>();
            dichan.put("id", 2);
            dichan.put("name", "地产棉");
            chandi.add(dichan);

            Map<String, Object> guochu = new HashMap<>();
            guochu.put("id", 3);
            guochu.put("name", "国储棉");
            chandi.add(guochu);

            Map<String, Object> jinkou = new HashMap<>();
            jinkou.put("id", 4);
            jinkou.put("name", "进口棉");
            chandi.add(jinkou);

            result.put("chandi", chandi);

            // def表示大导航 - 完全按照PHP原版
            Integer currentDef = def != null ? def : 1;
            result.put("def", currentDef);

            // 产地默认选项 - 完全按照PHP原版
            // TODO: 从数据库Cat表获取pid=currentDef的记录
            List<Map<String, Object>> chandiDefault = new ArrayList<>();
            if (currentDef == 1) { // 新疆棉的子产地
                Map<String, Object> aksu = new HashMap<>();
                aksu.put("id", 11);
                aksu.put("name", "阿克苏");
                chandiDefault.add(aksu);

                Map<String, Object> kashi = new HashMap<>();
                kashi.put("id", 12);
                kashi.put("name", "喀什");
                chandiDefault.add(kashi);

                Map<String, Object> bayinguoleng = new HashMap<>();
                bayinguoleng.put("id", 13);
                bayinguoleng.put("name", "巴音郭楞");
                chandiDefault.add(bayinguoleng);
            }
            result.put("chandi_default", chandiDefault);

            // 交货地选项 - 完全按照PHP原版
            // TODO: 从数据库Jiaohuodi表获取pid=0的记录
            List<Map<String, Object>> jiaohuodi = new ArrayList<>();
            Map<String, Object> shandong = new HashMap<>();
            shandong.put("id", 1);
            shandong.put("name", "山东");
            jiaohuodi.add(shandong);

            Map<String, Object> jiangsu = new HashMap<>();
            jiangsu.put("id", 2);
            jiangsu.put("name", "江苏");
            jiaohuodi.add(jiangsu);

            result.put("jiaohuodi", jiaohuodi);

            return Result.success(result);

        } catch (Exception e) {
            return Result.error("获取筛选选项失败: " + e.getMessage());
        }
    }

    /**
     * 获取商品列表（分页和过滤）
     *
     * @param pageQuery 分页和搜索参数
     * @return 商品分页
     */
    @GetMapping("/page")
    public Result<PageData<Store>> getStorePage(PageQuery pageQuery) {
        Page<Store> page = storeService.getStoreList(pageQuery);
        return Result.success(new PageData<>(page));
    }

    /**
     * 根据ID获取商品
     *
     * @param id 商品ID
     * @return 商品
     */
    @GetMapping("/{id}")
    public Result<Store> getStoreById(@PathVariable Long id) {
        return Result.success(storeService.getById(id));
    }

    /**
     * 添加商品
     *
     * @param storeDTO 商品数据
     * @return 结果
     */
    @PostMapping
    public Result<Boolean> addStore(@RequestBody StoreDTO storeDTO) {
        Store store = new Store();
        BeanUtils.copyProperties(storeDTO, store);
        return Result.success(storeService.addStore(store));
    }

    /**
     * 更新商品
     *
     * @param id 商品ID
     * @param storeDTO 商品数据
     * @return 结果
     */
    @PutMapping("/{id}")
    public Result<Boolean> updateStore(@PathVariable Long id, @RequestBody StoreDTO storeDTO) {
        storeDTO.setId(id);
        Store store = new Store();
        BeanUtils.copyProperties(storeDTO, store);
        return Result.success(storeService.updateStore(store));
    }

    /**
     * 删除商品
     *
     * @param id 商品ID
     * @return 结果
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteStore(@PathVariable Long id) {
        return Result.success(storeService.deleteStore(id));
    }

    /**
     * 批量删除商品
     *
     * @param ids 商品ID列表
     * @return 结果
     */
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteStores(@RequestBody List<Long> ids) {
        return Result.success(storeService.batchDeleteStores(ids));
    }

    /**
     * 更新商品状态
     *
     * @param id 商品ID
     * @param status 新状态
     * @return 结果
     */
    @PutMapping("/{id}/status")
    public Result<Boolean> updateStatus(@PathVariable Long id, @RequestParam String status) {
        return Result.success(storeService.updateStatus(id, status));
    }

    /**
     * 批量更新商品状态
     *
     * @param ids 商品ID列表
     * @param status 新状态
     * @return 结果
     */
    @PutMapping("/batch/status")
    public Result<Boolean> batchUpdateStatus(@RequestBody List<Long> ids, @RequestParam String status) {
        return Result.success(storeService.batchUpdateStatus(ids, status));
    }
}
