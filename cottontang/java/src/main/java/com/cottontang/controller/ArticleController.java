package com.cottontang.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cottontang.common.PageData;
import com.cottontang.common.Result;
import com.cottontang.dto.ArticleDTO;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Article;
import com.cottontang.service.ArticleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文章控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/articles")
public class ArticleController {

    @Autowired
    private ArticleService articleService;

    /**
     * 获取文章列表（分页）
     *
     * @param pageQuery 分页和搜索参数
     * @return 文章分页
     */
    @GetMapping("/page")
    public Result<PageData<Article>> getArticlePage(PageQuery pageQuery) {
        Page<Article> page = articleService.getArticleList(pageQuery);
        return Result.success(new PageData<>(page));
    }

    /**
     * 根据ID获取文章
     *
     * @param id 文章ID
     * @return 文章
     */
    @GetMapping("/{id}")
    public Result<Article> getArticleById(@PathVariable Long id) {
        return Result.success(articleService.getById(id));
    }

    /**
     * 根据分类ID获取文章
     *
     * @param catId 分类ID
     * @return 文章列表
     */
    @GetMapping("/category/{catId}")
    public Result<List<Article>> getArticlesByCategoryId(@PathVariable Long catId) {
        return Result.success(articleService.getArticlesByCategoryId(catId));
    }

    /**
     * 获取推荐文章
     *
     * @param limit 返回的最大文章数量
     * @return 推荐文章列表
     */
    @GetMapping("/recommended")
    public Result<List<Article>> getRecommendedArticles(@RequestParam(required = false) Integer limit) {
        return Result.success(articleService.getRecommendedArticles(limit));
    }

    /**
     * 添加文章
     *
     * @param articleDTO 文章数据
     * @return 结果
     */
    @PostMapping
    public Result<Boolean> addArticle(@RequestBody ArticleDTO articleDTO) {
        return Result.success(articleService.addArticle(articleDTO));
    }

    /**
     * 更新文章
     *
     * @param id 文章ID
     * @param articleDTO 文章数据
     * @return 结果
     */
    @PutMapping("/{id}")
    public Result<Boolean> updateArticle(@PathVariable Long id, @RequestBody ArticleDTO articleDTO) {
        articleDTO.setId(id);
        return Result.success(articleService.updateArticle(articleDTO));
    }

    /**
     * 删除文章
     *
     * @param id 文章ID
     * @return 结果
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteArticle(@PathVariable Long id) {
        return Result.success(articleService.deleteArticle(id));
    }

    /**
     * 切换文章推荐状态
     *
     * @param id 文章ID
     * @return 新的推荐状态
     */
    @PutMapping("/{id}/recommendation")
    public Result<Map<String, Integer>> toggleRecommendation(@PathVariable Long id) {
        Integer newStatus = articleService.toggleRecommendation(id);
        Map<String, Integer> result = new HashMap<>();
        result.put("isRecom", newStatus);
        return Result.success(result);
    }

    /**
     * 更新文章排序ID
     *
     * @param id 文章ID
     * @param orderId 新排序ID
     * @return 结果
     */
    @PutMapping("/{id}/order")
    public Result<Boolean> updateOrderId(@PathVariable Long id, @RequestParam Integer orderId) {
        return Result.success(articleService.updateOrderId(id, orderId));
    }

    /**
     * 更新文章状态
     *
     * @param id 文章ID
     * @param status 新状态
     * @return 结果
     */
    @PutMapping("/{id}/status")
    public Result<Boolean> updateStatus(@PathVariable Long id, @RequestParam Integer status) {
        Article article = articleService.getById(id);
        article.setStatus(status);
        return Result.success(articleService.updateById(article));
    }

    /**
     * 批量删除文章
     *
     * @param ids 文章ID列表
     * @return 结果
     */
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteArticles(@RequestBody List<Long> ids) {
        return Result.success(articleService.removeByIds(ids));
    }
}
