package com.cottontang.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cottontang.common.PageData;
import com.cottontang.common.Result;
import com.cottontang.dto.PageQuery;
import com.cottontang.dto.WarehouseDTO;
import com.cottontang.entity.Warehouse;
import com.cottontang.service.WarehouseService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 仓库控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/warehouse")
@RequiredArgsConstructor
public class WarehouseController {
    
    private final WarehouseService warehouseService;
    
    /**
     * 获取仓库列表（分页）
     * 
     * @param pageQuery 分页和搜索参数
     * @return 仓库分页
     */
    @GetMapping("/list")
    @SaCheckRole("admin")
    public Result<PageData<Warehouse>> list(PageQuery pageQuery) {
        Page<Warehouse> page = warehouseService.getWarehouseList(pageQuery);
        return Result.success(PageData.from(page));
    }
    
    /**
     * 根据ID获取仓库
     * 
     * @param id 仓库ID
     * @return 仓库详情
     */
    @GetMapping("/{id}")
    @SaCheckRole("admin")
    public Result<Warehouse> getById(@PathVariable Long id) {
        return Result.success(warehouseService.getById(id));
    }
    
    /**
     * 添加仓库
     * 
     * @param warehouseDTO 仓库数据
     * @return 成功或错误
     */
    @PostMapping
    @SaCheckRole("admin")
    public Result<Boolean> add(@Valid @RequestBody WarehouseDTO warehouseDTO) {
        return Result.success(warehouseService.addWarehouse(warehouseDTO));
    }
    
    /**
     * 更新仓库
     * 
     * @param warehouseDTO 仓库数据
     * @return 成功或错误
     */
    @PutMapping
    @SaCheckRole("admin")
    public Result<Boolean> update(@Valid @RequestBody WarehouseDTO warehouseDTO) {
        return Result.success(warehouseService.updateWarehouse(warehouseDTO));
    }
    
    /**
     * 删除仓库
     * 
     * @param id 仓库ID
     * @return 成功或错误
     */
    @DeleteMapping("/{id}")
    @SaCheckRole("admin")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.success(warehouseService.deleteWarehouse(id));
    }
    
    /**
     * 批量删除仓库
     * 
     * @param ids 仓库ID列表
     * @return 成功或错误
     */
    @DeleteMapping("/batch")
    @SaCheckRole("admin")
    public Result<Boolean> batchDelete(@RequestBody List<Long> ids) {
        boolean success = true;
        for (Long id : ids) {
            success &= warehouseService.deleteWarehouse(id);
        }
        return Result.success(success);
    }
    
    /**
     * 切换仓库推荐状态
     * 
     * @param id 仓库ID
     * @return 新的推荐状态
     */
    @PutMapping("/{id}/recom")
    @SaCheckRole("admin")
    public Result<Integer> toggleRecommendation(@PathVariable Long id) {
        return Result.success(warehouseService.toggleRecommendation(id));
    }
    
    /**
     * 更新仓库排序ID
     * 
     * @param id 仓库ID
     * @param orderId 新排序ID
     * @return 成功或错误
     */
    @PutMapping("/{id}/order")
    @SaCheckRole("admin")
    public Result<Boolean> updateOrderId(
            @PathVariable Long id,
            @RequestParam Integer orderId) {
        return Result.success(warehouseService.updateOrderId(id, orderId));
    }
    
    /**
     * 导出仓库到Excel
     * 
     * @param response HTTP响应
     * @throws IOException 如果发生I/O错误
     */
    @GetMapping("/export")
    @SaCheckRole("admin")
    public void exportToExcel(HttpServletResponse response) throws IOException {
        // 获取Excel数据
        byte[] excelData = warehouseService.exportToExcel();
        
        // 设置响应头
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String filename = URLEncoder.encode("warehouses_" + today + ".xlsx", StandardCharsets.UTF_8);
        
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename);
        response.setContentLength(excelData.length);
        
        // 将Excel数据写入响应
        response.getOutputStream().write(excelData);
        response.getOutputStream().flush();
    }
} 