package com.cottontang.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cottontang.common.PageData;
import com.cottontang.common.Result;
import com.cottontang.dto.AdvertisementDTO;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Advertisement;
import com.cottontang.service.AdvertisementService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 广告控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/advertisement")
@RequiredArgsConstructor
public class AdvertisementController {
    
    private final AdvertisementService advertisementService;
    
    /**
     * 获取广告列表（分页）
     * 
     * @param pageQuery 分页和搜索参数
     * @return 广告分页
     */
    @GetMapping("/list")
    @SaCheckRole("admin")
    public Result<PageData<Advertisement>> list(PageQuery pageQuery) {
        Page<Advertisement> page = advertisementService.getAdvertisementList(pageQuery);
        return Result.success(PageData.from(page));
    }
    
    /**
     * 根据类型获取广告
     * 
     * @param type 广告类型
     * @return 广告列表
     */
    @GetMapping("/type/{type}")
    @SaCheckRole("admin")
    public Result<List<Advertisement>> getByType(@PathVariable Integer type) {
        return Result.success(advertisementService.getAdvertisementsByType(type));
    }
    
    /**
     * 根据ID获取广告
     * 
     * @param id 广告ID
     * @return 广告详情
     */
    @GetMapping("/{id}")
    @SaCheckRole("admin")
    public Result<Advertisement> getById(@PathVariable Long id) {
        return Result.success(advertisementService.getById(id));
    }
    
    /**
     * 添加广告
     * 
     * @param advertisementDTO 广告数据
     * @return 成功或错误
     */
    @PostMapping
    @SaCheckRole("admin")
    public Result<Boolean> add(@Valid @RequestBody AdvertisementDTO advertisementDTO) {
        return Result.success(advertisementService.addAdvertisement(advertisementDTO));
    }
    
    /**
     * 更新广告
     * 
     * @param advertisementDTO 广告数据
     * @return 成功或错误
     */
    @PutMapping
    @SaCheckRole("admin")
    public Result<Boolean> update(@Valid @RequestBody AdvertisementDTO advertisementDTO) {
        return Result.success(advertisementService.updateAdvertisement(advertisementDTO));
    }
    
    /**
     * 删除广告
     * 
     * @param id 广告ID
     * @return 成功或错误
     */
    @DeleteMapping("/{id}")
    @SaCheckRole("admin")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.success(advertisementService.deleteAdvertisement(id));
    }
    
    /**
     * 批量删除广告
     * 
     * @param ids 广告ID列表
     * @return 成功或错误
     */
    @DeleteMapping("/batch")
    @SaCheckRole("admin")
    public Result<Boolean> batchDelete(@RequestBody List<Long> ids) {
        boolean success = true;
        for (Long id : ids) {
            success &= advertisementService.deleteAdvertisement(id);
        }
        return Result.success(success);
    }
    
    /**
     * 更新广告排序ID
     * 
     * @param id 广告ID
     * @param orderId 新排序ID
     * @return 成功或错误
     */
    @PutMapping("/{id}/order")
    @SaCheckRole("admin")
    public Result<Boolean> updateOrderId(
            @PathVariable Long id,
            @RequestParam Integer orderId) {
        return Result.success(advertisementService.updateOrderId(id, orderId));
    }
} 