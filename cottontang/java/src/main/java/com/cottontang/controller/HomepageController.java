package com.cottontang.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cottontang.dto.PageQuery;
import com.cottontang.common.Result;
import com.cottontang.entity.Homepage;
import com.cottontang.service.HomepageService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 首页数据控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/homepage")
@RequiredArgsConstructor
public class HomepageController {
    
    private final HomepageService homepageService;
    
    /**
     * 获取首页数据列表（公开接口）
     * 
     * @param pageQuery 分页查询参数
     * @return 首页数据分页
     */
    @GetMapping("/public/list")
    public Result<Page<Homepage>> getPublicHomepageList(PageQuery pageQuery) {
        return Result.success(homepageService.getHomepageList(pageQuery));
    }
    
    /**
     * 获取首页数据列表（管理员）
     * 
     * @param pageQuery 分页查询参数
     * @return 首页数据分页
     */
    @GetMapping("/admin/list")
    @SaCheckRole("admin")
    public Result<Page<Homepage>> getAdminHomepageList(PageQuery pageQuery) {
        return Result.success(homepageService.getHomepageList(pageQuery));
    }
    
    /**
     * 根据ID获取首页数据详情
     * 
     * @param id 首页数据ID
     * @return 首页数据详情
     */
    @GetMapping("/{id}")
    public Result<Homepage> getHomepageById(@PathVariable Long id) {
        Homepage homepage = homepageService.getById(id);
        return homepage != null ? Result.success(homepage) : Result.error("数据不存在");
    }
    
    /**
     * 创建首页数据
     * 
     * @param homepage 首页数据
     * @return 操作结果
     */
    @PostMapping
    @SaCheckRole("admin")
    public Result<String> createHomepage(@Valid @RequestBody Homepage homepage) {
        boolean success = homepageService.addHomepage(homepage);
        return success ? Result.success("首页数据创建成功") : Result.error("首页数据创建失败");
    }
    
    /**
     * 更新首页数据
     * 
     * @param id 首页数据ID
     * @param homepage 首页数据
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @SaCheckRole("admin")
    public Result<String> updateHomepage(@PathVariable Long id, @Valid @RequestBody Homepage homepage) {
        homepage.setId(id);
        boolean success = homepageService.updateHomepage(homepage);
        return success ? Result.success("首页数据更新成功") : Result.error("首页数据更新失败");
    }
    
    /**
     * 删除首页数据
     * 
     * @param id 首页数据ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @SaCheckRole("admin")
    public Result<String> deleteHomepage(@PathVariable Long id) {
        boolean success = homepageService.deleteHomepage(id);
        return success ? Result.success("首页数据删除成功") : Result.error("首页数据删除失败");
    }
    
    /**
     * 根据类型获取首页数据
     * 
     * @param danType 单据类型
     * @param pageQuery 分页参数
     * @return 首页数据分页
     */
    @GetMapping("/type/{danType}")
    public Result<Page<Homepage>> getHomepageByType(@PathVariable String danType, PageQuery pageQuery) {
        return Result.success(homepageService.getHomepageByType(danType, pageQuery));
    }
}
