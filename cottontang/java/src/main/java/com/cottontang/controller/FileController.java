package com.cottontang.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cottontang.common.PageData;
import com.cottontang.common.Result;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Admin;
import com.cottontang.entity.File;
import com.cottontang.service.FileService;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.MalformedURLException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

/**
 * 文件控制器，用于处理文件上传和管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/files")
public class FileController {

    @Autowired
    private FileService fileService;

    /**
     * 上传文件
     *
     * @param file 要上传的文件
     * @return 上传的文件信息
     */
    @PostMapping("/upload")
    public Result<Map<String, Object>> uploadFile(@RequestParam("file") MultipartFile file) {
        // 从会话中获取当前管理员用户ID
        Long adminId = StpUtil.getLoginIdAsLong();
        // 1 表示管理员用户类型
        Map<String, Object> result = fileService.uploadFile(file, adminId, 1);
        return Result.success(result);
    }

    /**
     * 获取带分页的文件列表
     *
     * @param pageQuery 分页和搜索参数
     * @return 文件分页
     */
    @GetMapping("/page")
    public Result<PageData<File>> getFilePage(PageQuery pageQuery) {
        Page<File> page = fileService.getFileList(pageQuery);
        return Result.success(new PageData<>(page));
    }

    /**
     * 根据ID获取文件
     *
     * @param id 文件ID
     * @return 文件信息
     */
    @GetMapping("/{id}")
    public Result<File> getFileById(@PathVariable Long id) {
        return Result.success(fileService.getFileById(id));
    }

    /**
     * 下载文件
     *
     * @param id 文件ID
     * @return 文件资源
     */
    @GetMapping("/download/{id}")
    public ResponseEntity<Resource> downloadFile(@PathVariable Long id) {
        try {
            // 获取文件信息
            File file = fileService.getFileById(id);

            // 创建文件资源
            Path filePath = Paths.get("upload/" + file.getFilePath());
            Resource resource = new UrlResource(filePath.toUri());

            // 检查文件是否存在
            if (!resource.exists()) {
                return ResponseEntity.notFound().build();
            }

            // 设置响应头
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(file.getContentType()))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + file.getOriginalName() + "\"")
                    .body(resource);

        } catch (MalformedURLException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 删除文件
     *
     * @param id 文件ID
     * @return 结果
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteFile(@PathVariable Long id) {
        return Result.success(fileService.deleteFile(id));
    }

    /**
     * 批量删除文件
     *
     * @param ids 文件ID列表
     * @return 结果
     */
    @DeleteMapping("/batch")
    public Result<Boolean> batchDeleteFiles(@RequestBody List<Long> ids) {
        return Result.success(fileService.batchDeleteFiles(ids));
    }
}
