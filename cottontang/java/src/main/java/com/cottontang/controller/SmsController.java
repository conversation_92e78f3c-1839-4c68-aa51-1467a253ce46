package com.cottontang.controller;

import com.cottontang.common.Result;
import jakarta.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 短信验证码控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class SmsController {

    /**
     * 发送手机验证码 - 完全按照PHP逻辑
     * 对应PHP的 Tools/getCodeAly 接口
     *
     * @param mobile 手机号
     * @param session HTTP会话
     * @return 发送结果
     */
    @GetMapping("/getCodeAly")
    public Result<Map<String, Object>> getCodeAly(@RequestParam String mobile, HttpSession session) {
        try {
            // 验证手机号格式
            if (!mobile.matches("^1\\d{10}$")) {
                Map<String, Object> result = new HashMap<>();
                result.put("code", 0);
                result.put("msg", "请输入正确的手机号");
                return Result.success(result);
            }

            // 生成6位随机验证码
            String code = String.format("%06d", new Random().nextInt(999999));

            // 存储到session中（完全按照PHP逻辑）
            session.setAttribute("mobile_code", code);
            session.setAttribute("mobile_number", mobile);

            // TODO: 这里应该调用真实的短信服务发送验证码
            // 目前为了测试，直接返回成功
            System.out.println("发送验证码到手机号: " + mobile + ", 验证码: " + code);

            Map<String, Object> result = new HashMap<>();
            result.put("code", 1);
            result.put("msg", "验证码已发送，请注意查收");

            return Result.success(result);

        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("msg", "验证码发送失败，请稍后重试");
            return Result.success(result);
        }
    }
}
