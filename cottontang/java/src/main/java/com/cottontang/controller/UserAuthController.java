package com.cottontang.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.cottontang.common.Result;
import com.cottontang.dto.LoginDTO;
import com.cottontang.dto.RegisterDTO;
import com.cottontang.dto.UserInfoDTO;
import com.cottontang.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户认证控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user-auth")
@RequiredArgsConstructor
public class UserAuthController {
    
    private final UserService userService;
    
    /**
     * 用户登录
     * 
     * @param loginDTO 登录参数
     * @return 令牌和用户信息
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@Valid @RequestBody LoginDTO loginDTO) {
        // 执行登录
        String token = userService.login(loginDTO);
        
        // 获取用户信息
        UserInfoDTO userInfo = userService.getUserInfo(StpUtil.getLoginIdAsLong());
        
        // 准备响应
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("userInfo", userInfo);
        
        return Result.success(result);
    }
    
    /**
     * 用户注册
     * 
     * @param registerDTO 注册参数
     * @return 注册结果
     */
    @PostMapping("/register")
    public Result<String> register(@Valid @RequestBody RegisterDTO registerDTO) {
        boolean success = userService.register(registerDTO);
        return success ? Result.success("注册成功，请等待审核") : Result.error("注册失败");
    }
    
    /**
     * 获取当前用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("/info")
    @SaCheckLogin
    public Result<UserInfoDTO> getUserInfo() {
        Long userId = StpUtil.getLoginIdAsLong();
        UserInfoDTO userInfo = userService.getUserInfo(userId);
        return Result.success(userInfo);
    }
    
    /**
     * 用户登出
     * 
     * @return 操作结果
     */
    @PostMapping("/logout")
    @SaCheckLogin
    public Result<String> logout() {
        userService.logout();
        return Result.success("登出成功");
    }
    
    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 是否存在
     */
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam String username) {
        boolean exists = userService.checkUsernameExists(username);
        return Result.success(exists);
    }
    
    /**
     * 修改密码
     * 
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 操作结果
     */
    @PostMapping("/change-password")
    @SaCheckLogin
    public Result<String> changePassword(@RequestParam String oldPassword, 
                                       @RequestParam String newPassword) {
        Long userId = StpUtil.getLoginIdAsLong();
        boolean success = userService.changePassword(userId, oldPassword, newPassword);
        return success ? Result.success("密码修改成功") : Result.error("密码修改失败");
    }
    
    /**
     * 更新用户信息
     * 
     * @param userInfo 用户信息
     * @return 操作结果
     */
    @PutMapping("/update-info")
    @SaCheckLogin
    public Result<String> updateUserInfo(@RequestBody UserInfoDTO userInfo) {
        Long userId = StpUtil.getLoginIdAsLong();
        
        // 创建用户对象并设置允许更新的字段
        com.cottontang.entity.User user = new com.cottontang.entity.User();
        user.setId(userId);
        user.setNickname(userInfo.getNickname());
        user.setEmail(userInfo.getEmail());
        user.setCompany(userInfo.getCompany());
        
        boolean success = userService.updateUser(user);
        return success ? Result.success("信息更新成功") : Result.error("信息更新失败");
    }
}
