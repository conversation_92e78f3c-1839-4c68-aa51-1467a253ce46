package com.cottontang.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.cottontang.common.Result;
import com.cottontang.dto.LoginDTO;
import com.cottontang.dto.UserInfoDTO;
import com.cottontang.service.AdminService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {
    
    private final AdminService adminService;
    
    /**
     * 管理员登录
     * 
     * @param loginDTO 登录参数
     * @return 令牌
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@Valid @RequestBody LoginDTO loginDTO) {
        // 执行登录
        String token = adminService.login(loginDTO);
        
        // 获取用户信息
        UserInfoDTO userInfo = adminService.getAdminInfo(StpUtil.getLoginIdAsLong());
        
        // 准备响应
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("userInfo", userInfo);
        
        return Result.success(result);
    }
    
    /**
     * 获取当前管理员信息
     * 
     * @return 管理员信息
     */
    @GetMapping("/info")
    public Result<UserInfoDTO> getUserInfo() {
        // 检查用户是否已登录
        StpUtil.checkLogin();
        
        // 获取用户信息
        UserInfoDTO userInfo = adminService.getAdminInfo(StpUtil.getLoginIdAsLong());
        
        return Result.success(userInfo);
    }
    
    /**
     * 管理员登出
     * 
     * @return 成功结果
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        adminService.logout();
        return Result.success();
    }
} 
