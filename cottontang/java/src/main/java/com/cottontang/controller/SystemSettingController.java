package com.cottontang.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.cottontang.common.Result;
import com.cottontang.entity.SystemSetting;
import com.cottontang.service.SystemSettingService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 系统设置控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system-settings")
@RequiredArgsConstructor
public class SystemSettingController {
    
    private final SystemSettingService systemSettingService;
    
    /**
     * 获取所有系统设置
     * 
     * @return 系统设置映射
     */
    @GetMapping
    @SaCheckRole("admin")
    public Result<Map<String, String>> getAllSettings() {
        return Result.success(systemSettingService.getAllSettings());
    }
    
    /**
     * 根据键获取设置值
     * 
     * @param key 设置键
     * @return 设置值
     */
    @GetMapping("/{key}")
    public Result<String> getSettingValue(@PathVariable String key) {
        String value = systemSettingService.getSettingValue(key);
        return Result.success(value);
    }
    
    /**
     * 根据分组获取设置列表
     * 
     * @param group 分组名
     * @return 设置列表
     */
    @GetMapping("/group/{group}")
    @SaCheckRole("admin")
    public Result<List<SystemSetting>> getSettingsByGroup(@PathVariable String group) {
        return Result.success(systemSettingService.getSettingsByGroup(group));
    }
    
    /**
     * 设置配置值
     * 
     * @param key 设置键
     * @param value 设置值
     * @return 操作结果
     */
    @PostMapping("/{key}")
    @SaCheckRole("admin")
    public Result<String> setSetting(@PathVariable String key, @RequestParam String value) {
        boolean success = systemSettingService.setSetting(key, value);
        return success ? Result.success("设置保存成功") : Result.error("设置保存失败");
    }
    
    /**
     * 批量设置配置
     * 
     * @param settings 设置映射
     * @return 操作结果
     */
    @PostMapping("/batch")
    @SaCheckRole("admin")
    public Result<String> setSettings(@RequestBody Map<String, String> settings) {
        boolean success = systemSettingService.setSettings(settings);
        return success ? Result.success("批量设置保存成功") : Result.error("批量设置保存失败");
    }
    
    /**
     * 删除设置
     * 
     * @param key 设置键
     * @return 操作结果
     */
    @DeleteMapping("/{key}")
    @SaCheckRole("admin")
    public Result<String> deleteSetting(@PathVariable String key) {
        boolean success = systemSettingService.deleteSetting(key);
        return success ? Result.success("设置删除成功") : Result.error("设置删除失败");
    }
}
