package com.cottontang.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cottontang.dto.PageQuery;
import com.cottontang.common.Result;
import com.cottontang.entity.CustomOrder;
import com.cottontang.service.CustomOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 定制订单控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/custom-orders")
@RequiredArgsConstructor
public class CustomOrderController {

    private final CustomOrderService customOrderService;

    /**
     * 获取定制订单列表（管理员）
     *
     * @param pageQuery 分页查询参数
     * @return 定制订单分页
     */
    @GetMapping("/admin/list")
    @SaCheckRole("admin")
    public Result<Page<CustomOrder>> getAdminCustomOrderList(PageQuery pageQuery) {
        return Result.success(customOrderService.getCustomOrderList(pageQuery));
    }

    /**
     * 获取用户定制订单列表
     *
     * @param pageQuery 分页查询参数
     * @return 定制订单分页
     */
    @GetMapping("/user/list")
    @SaCheckLogin
    public Result<Page<CustomOrder>> getUserCustomOrderList(PageQuery pageQuery) {
        Long uid = StpUtil.getLoginIdAsLong();
        return Result.success(customOrderService.getCustomOrdersByUserId(uid, pageQuery));
    }

    /**
     * 根据ID获取定制订单详情
     *
     * @param id 定制订单ID
     * @return 定制订单详情
     */
    @GetMapping("/{id}")
    @SaCheckLogin
    public Result<CustomOrder> getCustomOrderById(@PathVariable Long id) {
        CustomOrder customOrder = customOrderService.getById(id);

        // 检查权限：管理员或订单所有者
        if (!StpUtil.hasRole("admin")) {
            Long uid = StpUtil.getLoginIdAsLong();
            if (!uid.equals(customOrder.getUid())) {
                return Result.error("无权访问此订单");
            }
        }

        return Result.success(customOrder);
    }

    /**
     * 创建定制订单
     *
     * @param customOrder 定制订单数据
     * @return 操作结果
     */
    @PostMapping
    @SaCheckLogin
    public Result<String> createCustomOrder(@Valid @RequestBody CustomOrder customOrder) {
        // 设置用户ID
        customOrder.setUid(StpUtil.getLoginIdAsLong());

        boolean success = customOrderService.addCustomOrder(customOrder);
        return success ? Result.success("定制订单创建成功") : Result.error("定制订单创建失败");
    }

    /**
     * 更新定制订单
     *
     * @param id 定制订单ID
     * @param customOrder 定制订单数据
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @SaCheckLogin
    public Result<String> updateCustomOrder(@PathVariable Long id, @Valid @RequestBody CustomOrder customOrder) {
        CustomOrder existingOrder = customOrderService.getById(id);
        if (existingOrder == null) {
            return Result.error("定制订单不存在");
        }

        // 检查权限：管理员或订单所有者
        if (!StpUtil.hasRole("admin")) {
            Long uid = StpUtil.getLoginIdAsLong();
            if (!uid.equals(existingOrder.getUid())) {
                return Result.error("无权修改此订单");
            }
        }

        customOrder.setId(id);
        boolean success = customOrderService.updateCustomOrder(customOrder);
        return success ? Result.success("定制订单更新成功") : Result.error("定制订单更新失败");
    }

    /**
     * 删除定制订单
     *
     * @param id 定制订单ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @SaCheckRole("admin")
    public Result<String> deleteCustomOrder(@PathVariable Long id) {
        boolean success = customOrderService.deleteCustomOrder(id);
        return success ? Result.success("定制订单删除成功") : Result.error("定制订单删除失败");
    }

    /**
     * 更新订单状态
     *
     * @param id 订单ID
     * @param status 新状态
     * @return 操作结果
     */
    @PutMapping("/{id}/status")
    @SaCheckRole("admin")
    public Result<String> updateOrderStatus(@PathVariable Long id, @RequestParam Integer status) {
        boolean success = customOrderService.updateOrderStatus(id, status);
        return success ? Result.success("订单状态更新成功") : Result.error("订单状态更新失败");
    }
}
