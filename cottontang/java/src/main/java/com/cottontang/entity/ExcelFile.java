package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Excel文件记录实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_excel_file")
public class ExcelFile extends BaseEntity {
    
    /**
     * 文件标题
     */
    private String title;
    

    
    /**
     * 管理员用户ID
     */
    private Long adminUid;
    
    /**
     * 文件路径
     */
    private String filePath;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 处理状态（0: 未处理, 1: 处理中, 2: 已完成, 3: 失败）
     */
    private Integer status;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 导入记录数
     */
    private Integer recordCount;
}
