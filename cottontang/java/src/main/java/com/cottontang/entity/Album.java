package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 相册实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_xiangce")
public class Album extends BaseEntity {
    
    /**
     * 相册标题/活动名称
     */
    private String title;
    

    
    /**
     * 关联ID
     */
    private String linkId;
    
    /**
     * 缩略图
     */
    private String thumbs;
    
    /**
     * 分类ID
     */
    private Long catId;
    
    /**
     * 排序ID
     */
    private Integer orderId;
    
    /**
     * 主图片
     */
    private String pic;
    
    /**
     * 内容描述
     */
    private String content;
    
    /**
     * 摘要
     */
    private String summary;
    
    /**
     * 难度
     */
    private String nandu;
    
    /**
     * 名额
     */
    private Integer minge;
    
    /**
     * 原价
     */
    private Integer yuanPrice;
    
    /**
     * 现价
     */
    private Integer price;
    
    /**
     * 老师IDs
     */
    private String laoshiIds;
    
    /**
     * 老师关键词
     */
    private String laoshiKeywords;
    
    /**
     * 阶段ID
     */
    private Long jieduanId;
    
    /**
     * 年级ID
     */
    private Long nianjiId;
    
    /**
     * 科目ID
     */
    private Long kemuId;
    
    /**
     * 课程类型ID
     */
    private Long keTypeId;
}
