package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;


/**
 * 实体基类
 *
 * <AUTHOR>
 */
@Data
public class BaseEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间戳
     */
    @TableField(fill = FieldFill.INSERT)
    private Long addTime;

    /**
     * 更新时间戳
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateTime;

//    /**
//     * 逻辑删除标记 (0: 未删除, 1: 已删除)
//     */
//    @TableLogic
//    @TableField(fill = FieldFill.INSERT)
//    private Integer isDeleted;
}
