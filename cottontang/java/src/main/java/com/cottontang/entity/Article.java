package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文章实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_art")
public class Article extends BaseEntity {
    
    /**
     * 文章标题
     */
    private String title;
    
    /**
     * 文章分类ID
     */
    private Long catId;
    
    /**
     * 文章内容
     */
    private String content;
    
    /**
     * 文章缩略图
     */
    private String thumbs;
    
    /**
     * 排序ID
     */
    private Integer orderId;
    
    /**
     * 是否推荐（0: 否, 1: 是）
     */
    private Integer isRecom;
    

    
    /**
     * 文章描述
     */
    private String description;

    /**
     * 发布日期
     */
    private Long pubDate;

    /**
     * 文章状态（0: 草稿, 1: 已发布）
     */
    private Integer status;

    /**
     * 分类名称（临时字段）
     */
    @TableField(exist = false)
    private String catName;

    /**
     * 文章作者
     */
    private String author;

    /**
     * 文章来源
     */
    private String source;

    /**
     * 浏览次数
     */
    private Integer viewCount;
}