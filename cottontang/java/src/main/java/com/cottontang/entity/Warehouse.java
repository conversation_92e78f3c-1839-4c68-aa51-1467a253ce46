package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * Warehouse entity
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_cangku")
public class Warehouse extends BaseEntity {
    
    /**
     * Warehouse title/name
     */
    private String title;
    
    /**
     * Warehouse address
     */
    private String address;
    
    /**
     * Level 1 region ID
     */
    private Long level1;
    
    /**
     * Level 2 region ID
     */
    private Long level2;
    
    /**
     * Level 3 region ID
     */
    private Long level3;
    
    /**
     * Order ID for sorting
     */
    private Integer orderId;
    

    
    /**
     * Is recommended (0: No, 1: Yes)
     */
    private Integer isRecom;
    
    /**
     * Contact phone number
     */
    private String phone;
    
    /**
     * Warehouse thumbnail
     */
    private String thumbs;
    
    /**
     * Warehouse capacity
     */
    private String capacity;
    
    /**
     * Warehouse status (0: Disabled, 1: Enabled)
     */
    private Integer status;

    /**
     * Warehouse type
     */
    private Integer type;

    /**
     * Region name (transient field)
     */
    @TableField(exist = false)
    private String regionName;
} 