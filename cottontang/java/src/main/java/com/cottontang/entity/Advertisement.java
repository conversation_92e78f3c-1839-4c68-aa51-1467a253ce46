package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 广告实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_adv")
public class Advertisement extends BaseEntity {
    
    /**
     * 广告标题
     */
    private String title;
    
    /**
     * 广告类型
     */
    private Integer type;
    
    /**
     * 广告图片URL
     */
    private String thumbs;
    
    /**
     * 广告链接URL
     */
    private String url;
    
    /**
     * 排序ID
     */
    private Integer orderId;
    

    
    /**
     * 广告内容
     */
    private String content;

    /**
     * 广告状态（0: 禁用, 1: 启用）
     */
    private Integer status;

    /**
     * 开始时间戳
     */
    private Long startTime;

    /**
     * 结束时间戳
     */
    private Long endTime;
} 