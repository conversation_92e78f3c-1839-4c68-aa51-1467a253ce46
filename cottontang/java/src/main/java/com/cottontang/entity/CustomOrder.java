package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 定制订单实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_dingzhi")
public class CustomOrder extends BaseEntity {

    /**
     * 订制编号
     */
    private String dingzhiNo;

    /**
     * 批号/捆号
     */
    private String pihaoKunkao;

    /**
     * 类型
     */
    private String leixing;

    /**
     * 颜色级/品级
     */
    private String yansejiPinji;

    /**
     * 马值
     */
    private String mazhi;

    /**
     * 长度
     */
    private String changdu;

    /**
     * 强力
     */
    private String qiangli;

    /**
     * 含杂率
     */
    private String hanzalv;

    /**
     * 回潮率
     */
    private String huichaolv;

    /**
     * 公重(吨)
     */
    private String gongzhong;

    /**
     * 毛重(吨)
     */
    private String maozhaong;

    /**
     * 整齐度
     */
    private String zhengqidu;

    /**
     * 加工厂
     */
    private String jiagongchang;

    /**
     * 仓储名称
     */
    private String cangchumingcheng;

    /**
     * 基差
     */
    private String jicha;

    /**
     * 备注
     */
    private String beizhu;

    /**
     * 点价合约
     */
    private String dianjiaheyue;

    /**
     * 件数
     */
    private String baoshu;

    /**
     * 分类ID
     */
    private Long catId;

    /**
     * 排序ID
     */
    private Integer orderId;



    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 状态（0: 待处理, 1: 已处理, 2: 已取消）
     */
    private Integer status;

    /**
     * URL链接
     */
    private String url;

    /**
     * 创建时间戳
     */
    @TableField(fill = FieldFill.INSERT)
    private Long addTime;
}
