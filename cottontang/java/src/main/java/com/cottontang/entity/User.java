package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_user")
public class User extends BaseEntity {
    
    /**
     * 用户登录名（手机号）
     */
    private String username;
    
    /**
     * 用户密码（加密的）
     */
    private String password;
    
    /**
     * 用户昵称
     */
    private String nickname;
    
    /**
     * 用户手机号
     */
    private String mobile;
    
    /**
     * 用户邮箱
     */
    private String email;
    
    /**
     * 用户头像
     */
    private String avatar;
    
    /**
     * 用户状态（0: 禁用, 1: 启用）
     */
    private Integer status;
    
    /**
     * 用户类型
     */
    private Integer type;
    

    
    /**
     * 最后登录时间戳
     */
    private Long lastLogin;
    
    /**
     * 公司名称
     */
    private String company;
    
    /**
     * 最后登录IP
     */
    private String lastIp;

    /**
     * 会员类型（0: 普通, 1: VIP）
     */
    private Integer memberType;

    /**
     * 是否审核通过（0: 未审核, 1: 已审核）
     */
    private Integer isCheck;

    /**
     * 登录时间戳
     */
    private Long loginTime;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 用户类型（1: 个人, 2: 企业）
     */
    private Integer userType;

    /**
     * 证件类型
     */
    private String certificateType;

    /**
     * 证件号码
     */
    private String certificateNumber;

    /**
     * 排序ID
     */
    private Integer orderId;
} 