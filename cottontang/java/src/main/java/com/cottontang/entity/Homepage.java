package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

/**
 * 首页数据实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_shouye")
public class Homepage extends BaseEntity {
    
    /**
     * 序号
     */
    private String xvhao;
    
    /**
     * 批号/捆号
     */
    private String pihaoKunkao;
    
    /**
     * 类型
     */
    private String leixing;
    
    /**
     * 颜色级/品级
     */
    private String yansejiPinji;
    
    /**
     * 马值
     */
    private String mazhi;
    
    /**
     * 长度
     */
    private String changdu;
    
    /**
     * 强力
     */
    private String qiangli;
    
    /**
     * 含杂率
     */
    private String hanzalv;
    
    /**
     * 回潮率
     */
    private String huichaolv;
    
    /**
     * 公重(吨)
     */
    private String gongzhong;
    
    /**
     * 毛重(吨)
     */
    private String maozhaong;
    
    /**
     * 整齐度
     */
    private String zhengqidu;
    
    /**
     * 加工厂
     */
    private String jiagongchang;
    
    /**
     * 仓储名称
     */
    private String cangchumingcheng;
    
    /**
     * 基差
     */
    private Integer jicha;
    
    /**
     * 备注
     */
    private String beizhu;
    
    /**
     * 点价合约
     */
    private String dianjiaheyue;
    
    /**
     * 包数
     */
    private String baoshu;
    
    /**
     * 分类ID
     */
    private Long catId;
    
    /**
     * 排序ID
     */
    private Integer orderId;
    
    /**
     * 单据类型（仓单,在售,挂单,已售）
     */
    private String danType;
    
    /**
     * 产地
     */
    private String chandi;
    
    /**
     * 状态
     */
    private String zhuangtai;
    
    /**
     * 白棉1/2/3级
     */
    private BigDecimal bm123;
    

}
