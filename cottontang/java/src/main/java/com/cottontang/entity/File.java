package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_file")
public class File extends BaseEntity {
    
    /**
     * 原始文件名
     */
    private String originalName;
    
    /**
     * 存储文件名
     */
    private String storedName;
    
    /**
     * 文件路径
     */
    private String filePath;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 文件扩展名
     */
    private String extension;
    
    /**
     * 文件MIME类型
     */
    private String contentType;
    
    /**
     * 上传者ID（管理员或用户）
     */
    private Long uploaderId;
    
    /**
     * 上传者类型（1：管理员，2：用户）
     */
    private Integer uploaderType;
    
    /**
     * 文件类型（1：图片，2：文档，3：其他）
     */
    private Integer fileType;
} 