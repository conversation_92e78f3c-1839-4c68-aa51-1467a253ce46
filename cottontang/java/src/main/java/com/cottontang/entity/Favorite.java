package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户收藏实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_favor")
public class Favorite extends BaseEntity {
    
    /**
     * 用户ID
     */
    private Long uid;
    
    /**
     * 收藏的对象ID（商品ID、文章ID等）
     */
    private Long favorId;
    
    /**
     * 创建时间戳
     */
    private Long addTime;
    
    /**
     * 备注信息
     */
    private String memo;
    
    /**
     * 收藏类型（1: 商品, 2: 文章, 3: 其他）
     */
    private Integer favorType;
}
