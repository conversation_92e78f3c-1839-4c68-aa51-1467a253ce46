package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消息实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_msg")
public class Message extends BaseEntity {
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 创建时间戳
     */
    private Long addTime;
    
    /**
     * 消息类型
     */
    private String type;
    
    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * 用户ID
     */
    private Long uid;
    
    /**
     * 附件图片
     */
    private String thumbs;
    
    /**
     * 消息状态（0: 未读, 1: 已读）
     */
    private Integer status;
    
    /**
     * 发送者ID
     */
    private Long senderId;
    
    /**
     * 接收者ID
     */
    private Long receiverId;
}
