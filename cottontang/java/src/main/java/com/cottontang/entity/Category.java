package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 分类实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_cat")
public class Category extends BaseEntity {
    
    /**
     * 分类名称
     */
    private String name;
    
    /**
     * 父ID（0表示顶层）
     */
    private Long pid;
    
    /**
     * 排序ID
     */
    private Integer orderId;
    
    /**
     * 分类图片
     */
    private String img;
    
    /**
     * 分类描述
     */
    private String description;
    
    /**
     * 分类路径
     */
    private String path;
    
    /**
     * 子分类（临时字段）
     */
    @TableField(exist = false)
    private List<Category> children;
} 