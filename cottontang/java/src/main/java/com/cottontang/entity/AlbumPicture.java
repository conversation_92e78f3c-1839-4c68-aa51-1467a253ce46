package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 相册图片实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_xiangce_pic")
public class AlbumPicture extends BaseEntity {
    
    /**
     * 图片路径
     */
    private String thumbs;
    
    /**
     * 关联相册ID
     */
    private String linkId;
    
    /**
     * 图片标题
     */
    private String title;
    
    /**
     * 图片描述
     */
    private String description;
    
    /**
     * 排序
     */
    private Integer sort;
    

}
