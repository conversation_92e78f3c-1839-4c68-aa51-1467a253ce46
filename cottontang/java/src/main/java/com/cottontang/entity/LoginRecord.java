package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 登录记录实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_denglu")
public class LoginRecord extends BaseEntity {
    
    /**
     * 描述信息
     */
    private String desc;
    
    /**
     * 创建时间戳
     */
    private Long addTime;
    
    /**
     * 用户ID
     */
    private Long uid;
    
    /**
     * 用户文本信息
     */
    private String uidText;
    
    /**
     * 登录IP地址
     */
    private String ipAddress;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 登录类型（1: 管理员, 2: 普通用户）
     */
    private Integer loginType;
    
    /**
     * 登录状态（0: 失败, 1: 成功）
     */
    private Integer status;
}
