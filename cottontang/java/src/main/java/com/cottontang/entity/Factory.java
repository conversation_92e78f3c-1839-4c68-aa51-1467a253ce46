package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工厂实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_gongchang")
public class Factory extends BaseEntity {
    
    /**
     * 工厂名称
     */
    private String title;
    
    /**
     * 第一级区域ID
     */
    private Long level1;
    
    /**
     * 第二级区域ID
     */
    private Long level2;
    
    /**
     * 第三级区域ID
     */
    private Long level3;
    
    /**
     * 分类ID
     */
    private Long catId;
    
    /**
     * 排序ID
     */
    private Integer orderId;
    

    
    /**
     * 交货地
     */
    private String jiaohuodi;
    
    /**
     * 是否热门（0: 否, 1: 是）
     */
    private Integer isHot;
    
    /**
     * 工厂地址
     */
    private String address;
    
    /**
     * 联系电话
     */
    private String phone;
    
    /**
     * 工厂描述
     */
    private String description;
    
    /**
     * 工厂缩略图
     */
    private String thumbs;
}
