server:
  port: 8080

spring:
  application:
    name: cottontang
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************************************
    username: 44HkNsrw54ma2im.root
    password: whuWZu8hzpk0snkm
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      enabled: true
      max-file-size: 200MB
      max-request-size: 350MB
      file-size-threshold: 0
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.cottontang.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: is_deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# Sa-Token configuration
sa-token:
  token-name: Authorization
  timeout: 86400
  active-timeout: 1800
  is-concurrent: true
  is-share: false
  token-style: uuid
  is-log: true

# File upload configuration
file:
  upload:
    path: upload/
    allowed-extensions: jpg,jpeg,png,gif
    max-size: 10485760

# 阿里云短信服务配置
aliyun:
  sms:
    access-key-id: a
    access-key-secret: s
    sign-name: 青岛三匹马实业
    template-code: SMS_216120206
    endpoint: dysmsapi.aliyuncs.com
logging:
  level:
    com.cottontang: debug
    root: debug
